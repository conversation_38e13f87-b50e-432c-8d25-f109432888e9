// Monarch syntax highlighting for the st language.
export default {
    keywords: [
        'AND', 'ARRAY', 'AT', 'BO<PERSON>', 'BO<PERSON>#', 'BOOL#0', 'BOOL#1', 'BY', 'BYTE', 'CASE', 'CONFIGURATION', 'CONSTANT', 'CTD', 'CTD_DINT', 'CTD_LINT', 'CTD_UDINT', 'CTD_ULINT', 'CTU', 'CTUD', 'CTUD_DINT', 'CTUD_LINT', 'CTUD_ULINT', 'CTU_DINT', 'CTU_LINT', 'CTU_UDINT', 'CTU_ULINT', 'D#', 'DATE', 'DATE_AND_TIME', 'DINT', 'DO', 'DT', 'DT#', 'DWORD', 'ELSE', 'ELSIF', 'END_CASE', 'END_CONFIGURATION', 'END_FOR', 'END_FUNCTION', 'END_FUNCTION_BLOCK', 'END_IF', 'END_PROGRAM', 'END_REPEAT', 'END_RESOURCE', 'END_STRUCT', 'END_TYPE', 'END_VAR', 'END_WHILE', 'EXIT()', 'FALSE', 'FOR', 'FUNCTION', 'FUNCTION_BLOCK', 'F_EDGE', 'F_TRIG', 'IF', 'INT', 'INTERVAL', 'LINT', 'LREAL', 'LWORD', 'MOD', 'NON_RETAIN', 'NOT', 'OF', 'ON', 'OR', 'PRIORITY', 'PROGRAM', 'READ_ONLY', 'READ_WRITE', 'REAL', 'REPEAT', 'RESOURCE', 'RETAIN', 'RETURN()', 'RS', 'R_EDGE', 'R_TRIG', 'SINGLE', 'SINT', 'SR', 'STRING', 'STRUCT', 'T#', 'TASK', 'THEN', 'TIME', 'TIME_OF_DAY', 'TO', 'TOD', 'TOD#', 'TOF', 'TON', 'TP', 'TRUE', 'TYPE', 'UDINT', 'UINT', 'ULINT', 'UNTIL', 'USINT', 'VAR', 'VAR_ACCESS', 'VAR_CONFIG', 'VAR_EXTERNAL', 'VAR_GLOBAL', 'VAR_INPUT', 'VAR_IN_OUT', 'VAR_OUTPUT', 'VAR_STAT', 'VAR_TEMP', 'WHILE', 'WITH', 'WORD', 'WSTRING', 'XOR'
    ],
    operators: [
        '#', '&', '*', '**', '+', ',', '-', '.', '..', '/', ':', ':=', ';', '<', '<=', '<>', '=', '=>', '>', '>=', '|'
    ],
    symbols: /#|&|\(|\)|\*|\*\*|\+|,|-|\.|\.\.|\/|:|:=|;|<|<=|<>|=|=>|>|>=|\[|\]|\|/,
    tokenizer: {
        initial: [
            { regex: /\b2#(0|1)(0|1|(_))*\b/, action: { "token": "BINARY_INTEGER" } },
            { regex: /\b16#([0-9aAbBcCdDeEfF])([0-9aAbBcCdDeEfF]|(_))*\b/, action: { "token": "HEX_INTEGER" } },
            { regex: /\b8#([0-7])([0-7]|(_))*\b/, action: { "token": "OCTAL_INTEGER" } },
            { regex: /(')(\\.|[^'])*(')/, action: { "token": "SINGLE_BYTE_STRING" } },
            { regex: /(")(\\.|[^"])*(")/, action: { "token": "DOUBLE_BYTE_STRING" } },
            { regex: /((_)?((([0-9]+)(\.([0-9]+))?))[mM][sS])/, action: { "token": "TMILLISECONDS" } },
            { regex: /((_)?((([0-9]+)(\.([0-9]+))?))[mM])/, action: { "token": "TMINUTES" } },
            { regex: /((_)?((([0-9]+)(\.([0-9]+))?))[sS])/, action: { "token": "TSECONDS" } },
            { regex: /((_)?((([0-9]+)(\.([0-9]+))?))[dD])/, action: { "token": "TDAYS" } },
            { regex: /(((([0-9]+)(\.([0-9]+))?))[hH])/, action: { "token": "THOURS" } },
            { regex: /_/, action: { cases: { '@keywords': { "token": "keyword" }, '@default': { "token": "UNDERSCORE" } } } },
            { regex: /[0-9]+/, action: { cases: { '@keywords': { "token": "keyword" }, '@default': { "token": "DIGITS" } } } },
            { regex: /(([0-9]+)(\.([0-9]+))?)/, action: { cases: { '@keywords': { "token": "keyword" }, '@default': { "token": "FIXED_POINT" } } } },
            { regex: /%[IQM]\*/i, action: { "token": "INCOMPL_LOCATION_PREFIX" } },
            { regex: /%[IQM][XBWDL]?/i, action: { "token": "DIRECT_VARIABLE_PREFIX" } },
            { regex: /[_0-9]+/, action: { cases: { '@keywords': { "token": "keyword" }, '@default': { "token": "INTEGER" } } } },
            { regex: /([Ee][+-]?\d+)/, action: { "token": "EXPONENT" } },
            { regex: /\_?[a-zA-Z]{1}[a-zA-Z0-9_]*/, action: { cases: { '@keywords': { "token": "keyword" }, '@default': { "token": "ID" } } } },
            { include: '@whitespace' },
            { regex: /@symbols/, action: { cases: { '@operators': { "token": "operator" }, '@default': { "token": "" } } } },
        ],
        whitespace: [
            { regex: /\s+/, action: { "token": "white" } },
            { regex: /\(\*/, action: { "token": "comment", "next": "@comment" } },
            { regex: /\/\/[^\n\r]*/, action: { "token": "comment" } },
        ],
        comment: [
            { regex: /[^\(\*]+/, action: { "token": "comment" } },
            { regex: /\*\)/, action: { "token": "comment", "next": "@pop" } },
            { regex: /[\(\*]/, action: { "token": "comment" } },
        ],
    }
};
//# sourceMappingURL=st.monarch.js.map