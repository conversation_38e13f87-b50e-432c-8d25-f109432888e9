#include "compilegenerator.h"
#include <QBuffer>
#include "IDEProjectAndFile/projectandfilemanage.h"
#include <QCoreApplication>
#include <QStack>
#include "compilectrl.h"
#include <QTemporaryFile>
#include <QTime>
QString TargetGenerator::exeFilePath;
QVector<QString> TargetGenerator::fileVec;

void TargetGenerator::init()
{
    fileVec.clear();
    fileVec.append("fbc_user_code.c");
    fileVec.append("fbc_user_code.h");
    fileVec.append("lib_system_code.c");
    fileVec.append("lib_system_code.h");
    fileVec.append("program_code.c");
    fileVec.append("program_code.h");
    exeFilePath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\BuildFw\\Projects";
}

void TargetGenerator::toTarget_e2000(const QString &sourcePath, const QString &targetPath, bool *ret, QString * log)
{
    qDebug() << "enter toFile";
    QString logFilePath = ProjectAndFileManage::instance().getCurrentProjectPath() +
                          "\\" + ProjectAndFileManage::instance().getCurrentDeviceName() +
                          "\\toTargetLog.txt";
    QString destinationFilePath = ProjectAndFileManage::instance().getCurrentProjectPath() +
                                  "\\" + ProjectAndFileManage::instance().getCurrentDeviceName() +
                                  "\\oneos.bin";// 要拷贝到的地址
    // 如果目标文件存在，先删除它
    if (QFile::exists(destinationFilePath)) {
        if (!QFile::remove(destinationFilePath)) {
            qWarning() << "Failed to remove existing destination file!";
            if (ret != nullptr)*ret = false;
            if (log != nullptr) *log = "Failed to delete existing log file:" + destinationFilePath;
            return;
        }
    }

    if (QFile::exists(destinationFilePath)) {
        if (!QFile::remove(destinationFilePath)) {
            qWarning() << "Failed to remove existing destination file!";
            if (ret != nullptr)*ret = false;
            if (log != nullptr) *log = "Failed to delete existing log file:" + destinationFilePath;
            return;
        }
    }

    // 删除已有的日志文件
    if (QFile::exists(logFilePath)) {
        if (!QFile::remove(logFilePath)) {
            qWarning() << "Failed to delete existing log file:" << logFilePath;
            if (ret != nullptr) *ret = false;
            if (log != nullptr) *log = "Failed to delete existing log file:" + logFilePath;
            return;
        }
    }


    // 确保目标路径存在
    QDir dir(targetPath);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    QString codePath = targetPath + "/Projects/tgic_amp_tg800_64/application";
    qDebug() << "[INFO] codePath" << codePath;
    // 将文件从 sourcePath 复制到 targetPath
    for (const QString &file : fileVec) {
        QString sourceFile = sourcePath + "/" + file;
        QString targetFile = codePath + "/" + file;
        qDebug() << "[INFO] sourceFile"  << sourceFile << "targetFile" << targetFile;
        // 检查文件是否存在，避免复制失败
        if (QFile::exists(sourceFile)) {
            // 如果目标文件已存在，先删除
            if (QFile::exists(targetFile)) {
                QFile::remove(targetFile);
            }
            // 复制文件
            if (QFile::copy(sourceFile, targetFile)) {
                qDebug() << "Copied" << file;
            } else {
                qDebug() << "Failed to copy" << file;
            }
        }
    }

    qDebug() << "[INFO] compile path" << QFileInfo(QCoreApplication::applicationFilePath()).absolutePath()
             << targetPath;


    // 提前设置工作目录

    QSharedPointer<QProcess>& process = CompileCtrl::instance().compileProcess;
    process = QSharedPointer<QProcess>::create();
    process->setProgram("cmd.exe");
    process->setStandardOutputFile(logFilePath);
    process->setStandardErrorFile(logFilePath);

    process->setArguments(QStringList() << "/D" << "/C" << (QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\BuildFW\\Cube\\core\\init.bat &&"
                                                   " cd /d " + targetPath + "\\Projects\\tgic_amp_tg800_64 &&"
                                                   " oos clean &&"
                                                   " oos build -m o0"));
    process->start();
    process->waitForStarted();
    qDebug() << "[INFO] compile start" << QTime::currentTime();
    process->waitForFinished(-1);
    qDebug() << "[INFO] compile end"<< QTime::currentTime();

    // 将编译好的文件拷贝到目标路径
    QString sourceFilePath = targetPath + "\\Projects\\tgic_amp_tg800_64\\out\\oneos.bin"; // 编译好的文件
    // 检查源文件是否存在
    if (!QFile::exists(sourceFilePath)) {
        qWarning() << "Source file does not exist!";
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "compile target Failed!";
        return;
    }

    // 使用 QFile::copy 复制文件
    if (QFile::copy(sourceFilePath, destinationFilePath)) {
        qDebug() << "File copied successfully!";
    } else {
        qWarning() << "Failed to copy file!";
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "c2target,Compilation may not be successful";
        return;
    }

    // // if (ret != nullptr)*ret = true;
    // qDebug() << "leave toFile";
}

void TargetGenerator::toTarget_win(const QString &sourcePath, const QString &targetPath, bool *ret, QString *log)
{
    QString logFilePath = ProjectAndFileManage::instance().getCurrentProjectPath() +
                          "\\" + ProjectAndFileManage::instance().getCurrentDeviceName() +
                          "\\toTargetLog.txt";
    QString destinationFilePath = ProjectAndFileManage::instance().getCurrentProjectPath() +
                                  "\\" + ProjectAndFileManage::instance().getCurrentDeviceName() +
                                  "\\SANXIAPLC.exe";// 要拷贝到的地址
    // 如果目标文件存在，先删除它
    if (QFile::exists(destinationFilePath)) {
        if (!QFile::remove(destinationFilePath)) {
            qWarning() << "Failed to remove existing destination file!";
            if (ret != nullptr)*ret = false;
            if (log != nullptr) *log = "Failed to delete existing log file:" + destinationFilePath;
            return;
        }
    }
    // 删除已有的日志文件
    if (QFile::exists(logFilePath)) {
        if (!QFile::remove(logFilePath)) {
            qWarning() << "Failed to delete existing log file:" << logFilePath;
            if (ret != nullptr) *ret = false;
            if (log != nullptr) *log = "Failed to delete existing log file:" + logFilePath;
            return;
        }
    }

    // 确保目标路径存在
    QDir dir(targetPath);
    if (!dir.exists()) {
        dir.mkpath(".");
    }

    // 将文件从 sourcePath 复制到 targetPath
    for (const QString &file : fileVec) {
        QString sourceFile = sourcePath + "\\" + file;
        QString targetFile = targetPath + "\\" + file;
        // 检查文件是否存在，避免复制失败
        if (QFile::exists(sourceFile)) {
            // 如果目标文件已存在，先删除
            if (QFile::exists(targetFile)) {
                QFile::remove(targetFile);
            }
            // 复制文件
            if (QFile::copy(sourceFile, targetFile)) {
                qDebug() << "Copied" << file;
            } else {
                qDebug() << "Failed to copy" << file;
            }
        }
    }

    // 开启进程进行编译
    QProcess process;
    process.setProgram("cmd.exe");
    process.setStandardOutputFile(logFilePath);
    process.setStandardErrorFile(logFilePath);
    process.setArguments(QStringList() << "/C" << targetPath + "\\build.bat");
    process.setWorkingDirectory(QFileInfo(targetPath + "\\build.bat").absolutePath());
    process.start();
    process.waitForStarted();
    process.waitForFinished(10000);

    // 将编译好的文件拷贝到目标路径
    QString sourceFilePath = targetPath + "\\SANXIAPLC.exe"; // 编译好的文件
    // 检查源文件是否存在
    if (!QFile::exists(sourceFilePath)) {
        qWarning() << "Source file does not exist!";
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "compile target Failed!";
        return;
    }

    // 使用 QFile::copy 复制文件
    if (QFile::copy(sourceFilePath, destinationFilePath)) {
        qDebug() << "File copied successfully!";
    } else {
        qWarning() << "Failed to copy file!";
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "c2target,Compilation may not be successful";
        return;
    }

    // if (ret != nullptr)*ret = true;
    qDebug() << "leave toFile";
}

void TargetGenerator::prepareSrc_e2000(
    const QString &sourcePath, const QString& keyPath, const QString &targetPath, bool *ret, QString * log)
{
    // 删除编译目录
    CommonGenerator::removeDirectory(targetPath);

    // 复制编译工具到编译目录
    CommonGenerator::copyDirectoryContents(exeFilePath, targetPath + "/Projects");

    QString codePath = targetPath + "/Projects/tgic_amp_tg800_64/application";
    QDir dir;
    if (!dir.exists(codePath)) { // 检查文件夹是否存在
        bool success = dir.mkpath(codePath); // 创建文件夹
        if (success) {
            qDebug() << "Folder created successfully:" << codePath;
        } else {
            qDebug() << "Failed to create folder:" << codePath;
        }
    } else {
        CommonGenerator::removeDirectory(codePath);
        qDebug() << "Folder already exists:" << codePath;
    }

    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open file: " << keyPath;
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "Failed to open file,key";
        return;
    }
    QByteArray key = keyFile.readAll();
    QByteArray bytes;
    if(!decryption(sourcePath, key, bytes)){
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "Failed to decryption";
        return;
    }
    QBuffer buffer(&bytes);
    buffer.open(QIODevice::ReadOnly);

    QZipReader zipreader(&buffer);
    // QZipReader zipreader(sourcePath);
    qDebug() << "size: " << bytes.size();
    for(auto fileInfo : zipreader.fileInfoList()){
        QString filePath = fileInfo.filePath;
        QString targetFilePath = codePath + "/" + filePath;
        qDebug() << "targetFilePath" << targetFilePath;
        if(fileInfo.isFile){
            // 创建目标文件所在的目录
            QDir targetDir(QFileInfo(targetFilePath).absolutePath());
            if (!targetDir.exists()) {
                targetDir.mkpath(".");
            }
            QFile file(targetFilePath);
            if (file.open(QIODevice::WriteOnly)) {
                file.write(zipreader.fileData(filePath.toUtf8()));
                file.close();
            } else {
                qDebug() << "Failed to open file for writing:" << targetFilePath;
            }
        }
    }
    // if (ret != nullptr)*ret = true;
}

void TargetGenerator::prepareSrc_win(const QString &sourcePath, const QString &keyPath, const QString &targetPath, bool *ret, QString *log)
{
    // 删除编译目录
    CommonGenerator::removeDirectory(targetPath);

    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open file: " << keyPath;
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "Failed to open file,key";
        return;
    }
    QByteArray key = keyFile.readAll();
    QByteArray bytes;
    if(!decryption(sourcePath, key, bytes)){
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "Failed to decryption";
        return;
    }
    QBuffer buffer(&bytes);
    buffer.open(QIODevice::ReadOnly);

    QZipReader zipreader(&buffer);
    for(auto fileInfo : zipreader.fileInfoList()){
        QString filePath = fileInfo.filePath;
        QString targetFilePath = targetPath + "/" + filePath;
        if(fileInfo.isFile){
            // 创建目标文件所在的目录
            QDir targetDir(QFileInfo(targetFilePath).absolutePath());
            if (!targetDir.exists()) {
                targetDir.mkpath(".");
            }
            QFile file(targetFilePath);
            if (file.open(QIODevice::WriteOnly)) {
                file.write(zipreader.fileData(filePath.toUtf8()));
                file.close();
            } else {
                qDebug() << "Failed to open file for writing:" << targetFilePath;
            }
        }
    }
    // if (ret != nullptr)*ret = true;
}

TargetGenerator::TargetGenerator()
{

}

bool CommonGenerator::removeDirectory(const QString &dirPath)
{
    QDir dir(dirPath);

    // 检查目录是否存在
    if (!dir.exists()) {
        qDebug() << "Directory does not exist:" << dirPath;
        return false;
    }

    // 获取目录中的所有文件和子目录
    QStringList entries = dir.entryList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);

    // 遍历并删除所有文件和子目录
    for (const QString& entry : entries) {
        QString fullPath = dir.filePath(entry);

        if (QDir(fullPath).exists()) {
        // 如果是目录，递归删除
           if (!removeDirectory(fullPath)) {
                return false; // 如果递归删除失败，返回 false
            }
        } else {
            // 如果是文件，直接删除
            if (!QFile::remove(fullPath)) {
                qDebug() << "Failed to remove file:" << fullPath;
                return false; // 如果删除文件失败，返回 false
            }
        }
    }

    dir.rmdir(dirPath);

    return true;
}

bool CommonGenerator::copyDirectory(const QString &sourceDir, const QString &targetDir)
{
    QDir sourceDirectory(sourceDir);
    QDir targetDirectory;

    // 如果目标目录不存在，则创建它
    if (!targetDirectory.mkpath(targetDir)) {
        qWarning() << "Failed to create target directory:" << targetDir;
        return false;
    }

    // 获取源目录中的所有文件和子目录
    QFileInfoList entries = sourceDirectory.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);

    for (const QFileInfo &entry : entries) {
        QString sourcePath = entry.filePath();
        QString targetPath = targetDir + "/" + entry.fileName();

        if (entry.isDir()) {
            // 如果是目录，递归复制
            if (!copyDirectory(sourcePath, targetPath)) {
                return false;
            }
        } else {
            // 如果是文件，直接复制
            if (!QFile::copy(sourcePath, targetPath)) {
                qWarning() << "Failed to copy file:" << sourcePath << "to" << targetPath;
                return false;
            }
        }
    }

    return true;
}

bool CommonGenerator::copyDirectoryContents(const QString &sourceDirPath, const QString &destinationDirPath) {
    QDir sourceDir(sourceDirPath);
    QDir destinationDir(destinationDirPath);

    // 如果源文件夹不存在，返回false
    if (!sourceDir.exists()) {
        qWarning() << "Source directory does not exist!";
        return false;
    }

    // 如果目标文件夹不存在，则创建它
    if (!destinationDir.exists()) {
        if (!destinationDir.mkpath(".")) {
            qWarning() << "Failed to create destination directory!";
            return false;
        }
    }

    // 获取源文件夹中的所有文件和子文件夹
    QStringList files = sourceDir.entryList(QDir::Files);
    QStringList dirs = sourceDir.entryList(QDir::Dirs | QDir::NoDotAndDotDot);

    // 复制文件
    foreach (const QString &file, files) {
        QString sourceFilePath = sourceDir.absoluteFilePath(file);
        QString destFilePath = destinationDir.absoluteFilePath(file);

        if (!QFile::copy(sourceFilePath, destFilePath)) {
            qWarning() << "Failed to copy file:" << sourceFilePath;
            return false;
        }
    }

    // 递归复制子文件夹
    foreach (const QString &dir, dirs) {
        QString sourceSubDirPath = sourceDir.absoluteFilePath(dir);
        QString destSubDirPath = destinationDir.absoluteFilePath(dir);

        if (!copyDirectoryContents(sourceSubDirPath, destSubDirPath)) {
            return false;
        }
    }
    return true;
}

bool CommonGenerator::readLogFile(const QString &logPath, QString &logContent)
{
    QFile logFile(logPath);
    if (!logFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Failed to open log file:" << logPath;
        return false;
    }

    QTextStream in(&logFile);
    QString fileContent = in.readAll();
    logFile.close();

    // 使用正则表达式匹配所有 "raise ValueError"
    QRegularExpression regex(
        R"((Error message begins)(?:(?1)|.(?!Error message))(Error message end))",
        QRegularExpression::DotMatchesEverythingOption | QRegularExpression::InvertedGreedinessOption
        );
    QRegularExpressionMatchIterator matchIterator = regex.globalMatch(fileContent);
    if (!matchIterator.hasNext()) {
        return false;
    }

    QStringList matches;
    while (matchIterator.hasNext()) {
        QRegularExpressionMatch match = matchIterator.next();
        QString matchedContent = match.captured(1);
        matches.append(matchedContent);
    }

    logContent = matches.join("\n");

    return true;
}

bool CommonGenerator::getLastErro(const QString &logPath, CompileErroInfo& info)
{
    QFile logFile(logPath);
    if (!logFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Failed to open log file:" << logPath;
        return false;
    }

    QTextStream in(&logFile);
    QString fileContent = in.readAll();
    QString content = getLastErroBlock(fileContent);

    if(fileContent.contains("任务执行出错:")){
        in.seek(0);
        while (!in.atEnd()) {
            QString line = in.readLine();
            if (line.contains("任务执行出错:", Qt::CaseInsensitive)) {
                qDebug() << "Error line:" << line.trimmed();
                info.code = line.trimmed();
                logFile.close();
                return false;
            }
        }
    }
    logFile.close();
    QRegularExpression appRegex("app_name:\\s*(\\S+\\.(?:ST|st|C|c|FBD|LD|ld|SFC|sfc))");
    QRegularExpression loopRegex("code:\\s*\\(\\*{2,3}\\s*(.*?)\\s*\\*{2,3}\\)");
    // 查找所有匹配项
    QRegularExpressionMatchIterator appItr = appRegex.globalMatch(content);
    QRegularExpressionMatchIterator codeItr = loopRegex.globalMatch(content);

    QString lastAppName;
    QString lastCode;
    QString reason;

    // 遍历所有匹配项，保留最后一个
    while (appItr.hasNext()) {
        QRegularExpressionMatch match = appItr.next();
        lastAppName = match.captured(1);
    }

    while (codeItr.hasNext()) {
        QRegularExpressionMatch match = codeItr.next();
        lastCode = match.captured(1);
        int codeEndPos = match.capturedEnd();
        reason = content.mid(codeEndPos).trimmed();
    }

    if (!lastAppName.isEmpty() && !lastCode.isEmpty()) {
        info.app_name = lastAppName;
        info.code = lastCode;
        info.reason = reason;
    } else {
        return false;
    }

    return true;
}

// QString CommonGenerator::getLastErroBlock(QString fileContent)
// {
//     qDebug() << "getLastErroBlock";
//     QStack<int> stack;
//     QStringList matches;

//     int beginPos = -1;
//     int endPos = -1;

//     for (int i = 0; i < fileContent.size(); ++i) {
//         // 查找 "Error message begins" 的位置
//         if (i + strlen("Error message begins") - 1 < fileContent.size() &&
//             fileContent.mid(i, strlen("Error message begins")) == "Error message begins") {
//             if (stack.isEmpty()) {
//                 beginPos = i; // 记录起始位置
//             }
//             stack.push(i); // 入栈
//             i += strlen("Error message begins") - 1; // 跳过当前匹配的字符串
//         }
//         // 查找 "Error message end" 的位置
//         else if (i + strlen("Error message end") - 1 < fileContent.size() &&
//                  fileContent.mid(i, strlen("Error message end")) == "Error message end") {
//             if (!stack.isEmpty()) {
//                 stack.pop(); // 出栈
//                 if (stack.isEmpty()) {
//                     endPos = i + strlen("Error message end") - 1; // 记录结束位置
//                     // 提取从 beginPos 到 endPos 的内容
//                     QString matchedContent = fileContent.mid(beginPos, endPos - beginPos + 1);
//                     matches.append(matchedContent);
//                 }
//                 i += strlen("Error message end") - 1; // 跳过当前匹配的字符串
//             }
//         }
//     }
//     if(matches.size() > 0){
//         if(matches[matches.size() -1].contains("Error message begins")){
//             fileContent = getLastErroBlock(matches[matches.size() -1]);
//         }
//         return fileContent;
//     }
//     return "";
// }

QString CommonGenerator::getLastErroBlock(const QString &fileContent)
{
    QStringList list1 = fileContent.split("Error message begins");
    if (list1.size() < 2) {
        return "";
    }
    QStringList list2 = list1.last().split("Error message end");
    if (list2.isEmpty()) {
        return "";
    }
    return list2.first();
}

bool CommonGenerator::checkLogForString(const QString &logPath, const QString &targetString,
                                        const QString& failStr, QString * log)
{
    QFile logFile(logPath);
    if (!logFile.open(QIODevice::ReadOnly | QIODevice::Text)) {
        qWarning() << "Failed to open log file:" << logPath;
        return false;
    }

    QTextStream in(&logFile);
    QString logContent = in.readAll();
    

    // int contentLength = logContent.length();
    // int targetLength = targetString.length();

    // // 如果目标字符串长度为0，视为未找到
    // if (targetLength == 0) {
    //     return false;
    // }

    // // 获取最后100个字符，或者文件全部内容（如果不足100字符）
    // int startpos = contentLength > 100 ? contentLength - 100 : 0;
    // QString lastPart = logContent.mid(startpos);

    if((failStr.size() > 0) && (logContent.contains(failStr))){
        in.seek(0);
        while (!in.atEnd()) {
            QString line = in.readLine();
            if (line.contains("任务执行出错:", Qt::CaseInsensitive)) {
                *log = line.trimmed();
                logFile.close();
                return false;
            }
        }
    }
    logFile.close();
    // 判断是否包含目标字符串
    return logContent.contains(targetString);
}

bool TargetGenerator::decryption(const QString &encryptedFilePath, const QByteArray &key, QByteArray &decryptedData)
{
    if (key.isEmpty()) {
        qWarning() << "Key is empty!";
        return false;
    }

    QFile encryptedFile(encryptedFilePath);
    if (!encryptedFile.open(QIODevice::ReadOnly)) {
        qWarning() << "Failed to open encrypted file for reading:" << encryptedFilePath;
        return false;
    }

    // 读取文件的所有内容
    QByteArray encryptedContent = encryptedFile.readAll();

    // 检查文件长度是否足够
    if (encryptedContent.size() < 129) { // 假设头部长度是129字节
        qWarning() << "Encrypted file is too short!";
        encryptedFile.close();
        return false;
    }

    // 分离头部和加密数据
    QByteArray head = encryptedContent.left(129);
    QByteArray encryptedData = encryptedContent.mid(129);

    // 计算密钥起始位置
    quint8 start = head.at(88) % key.size();

    // 解密数据
    decryptedData.resize(encryptedData.size());
    for (int i = 0; i < encryptedData.size(); ++i) {
        int keyIndex = (start + i) % key.size();
        decryptedData[i] = encryptedData[i] ^ key[keyIndex];
    }

    encryptedFile.close();
    return true;
}

bool STGenerator::prepareDir()
{
    QString projectPath = ProjectAndFileManage::instance().getCurrentProjectPath();
    QString deviceName = ProjectAndFileManage::instance().getCurrentDeviceName();
    CommonGenerator::removeDirectory(projectPath + "/" + deviceName + "/User/Core1/work");
    CommonGenerator::removeDirectory(projectPath + "/" + deviceName + "/User/UserFBs/work");
    QList<FileList> fileList = ProjectAndFileManage::instance().getFileListBySettingName(deviceName);
    QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open file: " << keyPath;
        return false;
    }
    QByteArray key = keyFile.readAll();
    for(const FileList &fileInfo : fileList){
        QString sourcePath = fileInfo.absolutePath;
        QFileInfo fileInfoObj(sourcePath);
        QDir sourceDir = fileInfoObj.dir();

        // 创建目标目录 work
        QDir targetDir = sourceDir;
        QString workDirPath = sourceDir.filePath("work");
        if (!targetDir.exists("work")) {
            if (!targetDir.mkdir("work")) {
                qWarning() << "Failed to create work directory:" << workDirPath;
                return false;
            }
        }

        QString targetPath = workDirPath + "/" +fileInfoObj.fileName();
        // 加密文件解密后写入
        if(fileInfo.havePassword.size() > 0){
            QByteArray bytes;
            TargetGenerator::decryption(sourcePath, key, bytes);
            QFile encryptedFile(targetPath);
            if(!encryptedFile.open(QIODevice::WriteOnly)){
                qDebug() << "Failed to open file: " << targetPath;
                return false;
            }
            encryptedFile.write(bytes);
        }
        // 未加密的文件直接copy
        else{

            if (!QFile::copy(sourcePath, targetPath)) {
                qWarning() << "Failed to copy file from" << sourcePath << "to" << targetPath;
                return false;
            } else {
                qDebug() << "File copied successfully from" << sourcePath << "to" << targetPath;
            }
        }
    }
    return true;
}

void STGenerator::code2St(const QString &path, bool *ret, QString * log)
{
    prepareDir();
    // 开启进程进行编译
    QSharedPointer<QProcess>& process = CompileCtrl::instance().compileProcess;
    process = QSharedPointer<QProcess>::create();
    // process.
    QString logFilePath = ProjectAndFileManage::instance().getCurrentProjectPath() +
                          "\\" + ProjectAndFileManage::instance().getCurrentDeviceName() +
                          "\\code2Stlog.txt";

    // 删除已有的日志文件
    if (QFile::exists(logFilePath)) {
        if (!QFile::remove(logFilePath)) {
            qWarning() << "Failed to delete existing log file:" << logFilePath;
            if (ret != nullptr) *ret = false;
            if (log != nullptr) *log = "Failed to delete existing log file:" + logFilePath;
            return;
        }
    }

    process->setStandardOutputFile(logFilePath);
    process->setStandardErrorFile(logFilePath);
    process->setWorkingDirectory(QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/code2st_dist");
    // 构建命令行参数
    QStringList arguments;
    arguments << "--path_user" << path;

    // 启动进程
    qDebug() << "code2st.start" << QDateTime::currentDateTime();
    process->start(QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/code2st_dist/testx_main", arguments);

    // 等待进程完成
    if (!process->waitForFinished(-1)) { // -1 表示无限期等待
        qDebug() << QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/code2st_dist/testx_main";
        qWarning() << "code2St Process failed to start or crashed";
        qWarning() << "Error:" << process->errorString();
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "code2St Process failed to start or crashed\nError:" + process->errorString();
    } else {
        qDebug() << "Process finished successfully";
        qDebug() << "code2st.end" << QDateTime::currentDateTime();
        // 判断是否编译成功
        if (!CommonGenerator::checkLogForString(logFilePath, "code2st accomplished", "任务执行出错", log)) {
            qWarning() << "Compilation may not be successful";
            if (ret != nullptr)*ret = false;
            if (log != nullptr && log->size() < 1) *log = "code2st,Compilation may not be successful";
        } else {
            qDebug() << "Compilation was successful";
            // if (ret != nullptr)*ret = true;
        }
    }
}
void CodeGenerator::st2C(const QString &path, bool *ret, QString * log)
{
    // 开启进程进行编译
    QSharedPointer<QProcess>& process = CompileCtrl::instance().compileProcess;
    process = QSharedPointer<QProcess>::create();
    QString logFilePath = ProjectAndFileManage::instance().getCurrentProjectPath() +
                          "\\" + ProjectAndFileManage::instance().getCurrentDeviceName() +
                          "\\st2Clog.txt";

    // 删除已有的日志文件
    if (QFile::exists(logFilePath)) {
        if (!QFile::remove(logFilePath)) {
            qWarning() << "Failed to delete existing log file:" << logFilePath;
            if (ret != nullptr) *ret = false;
            if (log != nullptr) *log = "Failed to delete existing log file:" + logFilePath;
            return;
        }
    }

    process->setStandardOutputFile(logFilePath);
    process->setStandardErrorFile(logFilePath);
    process->setWorkingDirectory(QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/new_c_dist");
    // 构建命令行参数
    QStringList arguments;
    arguments << "--path_user" << path;

    // 启动进程
    qDebug() << "st2c.start" << QDateTime::currentDateTime();
    process->start(QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/new_c_dist/testx_main", arguments);
    // 等待进程完成
    if (!process->waitForFinished(-1)) { // -1 表示无限期等待
        qWarning() << "st2C Process failed to start or crashed";
        qWarning() << "Error:" << process->errorString();
        if (ret != nullptr)*ret = false;
        if (log != nullptr) *log = "code2St Process failed to start or crashed\nError:" + process->errorString();
    } else {
        qDebug() << "Process finished successfully";
        qDebug() << "st2c.end" << QDateTime::currentDateTime();
        // 判断是否编译成功
        if (!CommonGenerator::checkLogForString(logFilePath, "new_c accomplished", "任务执行出错", log)) {
            qWarning() << "Compilation may not be successful";
            if (ret != nullptr)*ret = false;
            if (log != nullptr && log->size() < 1)*log = "st2c,Compilation may not be successful";
        } else {
            qDebug() << "Compilation was successful";
            // if (ret != nullptr)*ret = true;
        }
    }
}
