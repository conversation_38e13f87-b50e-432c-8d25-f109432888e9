﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/qml"
import "qrc:/SimpleUI/qml"
import "qrc:/qml/control/common"

Rectangle {
    id: force_table

    property string deviceName
    property var selectAll: ({})

    readonly property var isTrigger: ["不使用", "一次", "持续"]

    S_Tabs {
        id: tabs
        anchors.fill: parent

        rightExtra: Row {
            height: parent.height
            spacing: 6

            QkButton {
                text: "添加表"
                width: 80
                height: 25
                anchors.verticalCenter: parent.verticalCenter

                onClicked: {
                    popup.raiseItem = add_table_item
                    popup.open()
                }
            }

            QkButton {
                text: "开 启"
                width: 60
                height: 25
                anchors.verticalCenter: parent.verticalCenter
            }
        }

        onTabChanged: {
            console.log(key)
        }

        onTabClose: {
            console.log(key)
        }
    }

    Component {
        id: tree_table_component

        Rectangle {
            id: table_item
            property string key
            property string tab
            property var tableData
            property alias table_view: tree_table

            ColumnLayout {
                id: column_layout
                anchors.fill: parent
                spacing: 0

                RowLayout {
                    id: row_layout
                    Layout.fillWidth: true
                    Layout.maximumHeight: 36
                    Layout.leftMargin: 10
                    Layout.rightMargin: 10
                    spacing: 10

                    QkButton {
                        id: add_var
                        text: "添加变量"
                        Layout.preferredWidth: 80
                        Layout.preferredHeight: 25

                        onClicked: {
                            const component = Qt.createComponent(
                                                "qrc:/SimpleUI/qml/S_VarSelectTable.qml")
                            if (component.status === Component.Ready) {
                                popup.raiseItem = component
                                popup.open()
                                popup.loadercenter.item.deviceName = force_table.deviceName
                                popup.loadercenter.item.varTypeList = ["Global", "IO", "M"]
                                popup.loadercenter.item.filterFun = varData => {
                                    const dataSource = tabs.findPage(
                                        tabs.activeKey).table_view.dataSource
                                    for (var i = 0; i < dataSource.length; i++) {
                                        if (varData.vid === dataSource[i].varId) {
                                            return false
                                        }
                                    }
                                    return true
                                }
                                popup.loadercenter.item.getBindData()
                                popup.loadercenter.item.onCancel.connect(() => {
                                                                             popup.close()
                                                                         })
                                popup.loadercenter.item.onConfirm.connect(
                                            addVariable)
                            }
                        }
                    }

                    QkButton {
                        id: del_var
                        text: "删除变量"
                        Layout.preferredWidth: 80
                        Layout.preferredHeight: 25

                        onClicked: {
                            const list = tree_table.selectionModel()
                            if (list.length) {
                                messageDialog.show("是否确认删除选中的变量", () => {
                                                       list.forEach(item => {
                                                                        VariableManage.deleteForcedVariable(
                                                                            Number(item.data._key))
                                                                    })
                                                       let tableData = VariableManage.getForcedList(
                                                           force_table.deviceName,
                                                           tabs.activeKey)
                                                       tableData = loadVarData(
                                                           tableData)
                                                       tabs.findPage(
                                                           tabs.activeKey).table_view.dataSource
                                                       = tableData
                                                       force_table.selectAll[tabs.activeKey] = false
                                                   }, () => {}, "confirm")
                            } else {
                                messageDialog.show("还未选中任何变量")
                            }
                        }
                    }
                }

                O_TreeTable {
                    id: tree_table
                    Layout.fillWidth: true
                    Layout.fillHeight: true

                    dataSource: tableData
                    columnSource: [{
                            "title": "ID",
                            "dataIndex": "_key",
                            "width": 60,
                            "minimumWidth": 60,
                            "maximumWidth": 60,
                            "readOnly": true
                        }, {
                            "title": customItem(column_checkbox, {
                                                    "table_key": table_item.key
                                                }),
                            "dataIndex": 'isChecked',
                            "width": 50,
                            "minimumWidth": 50,
                            "maximumWidth": 50
                        }, {
                            "title": '变量名称',
                            "dataIndex": 'forceName',
                            "width": (table_item.width - 450) / 5,
                            "readOnly": true
                        }, {
                            "title": '作用域',
                            "dataIndex": 'scope',
                            "width": 100,
                            "readOnly": true
                        }, {
                            "title": '数据类型',
                            "dataIndex": 'dataType',
                            "width": 100,
                            "readOnly": true
                        }, {
                            "title": '监视值',
                            "dataIndex": 'monitoredValue',
                            "width": (table_item.width - 450) / 5,
                            "readOnly": true
                        }, {
                            "title": '显示格式',
                            "dataIndex": 'displayFormat',
                            "editDelegate": com_display_format,
                            "width": 140
                        }, {
                            "title": '使用触发器强制',
                            "dataIndex": 'monitoringWithTrigger',
                            "editDelegate": com_trigger,
                            "width": (table_item.width - 450) / 5
                        }, {
                            "title": '强制值',
                            "dataIndex": 'forcedValue',
                            "editDelegate": com_forced_value,
                            "width": (table_item.width - 450) / 5
                        }, {
                            "title": '强制注释',
                            "dataIndex": 'description',
                            "editDelegate": com_description,
                            "width": (table_item.width - 450) / 5
                        }]
                }
            }
        }
    }

    Component {
        id: column_checkbox
        Item {
            S_CheckBox {
                anchors.centerIn: parent
                checked: Boolean(force_table.selectAll[options.table_key])
                Layout.alignment: Qt.AlignVCenter
                onCheckedChanged: {
                    if (checked === force_table.selectAll[options.table_key])
                        return
                    force_table.selectAll[options.table_key] = checked
                    Qt.callLater(() => {
                                     for (var i = 0; i < tableView.model.dataSourceSize; i++) {
                                         tableView.model.checkRow(i, checked)
                                     }
                                 })
                }
            }
        }
    }

    Component {
        id: com_checkbox
        Item {
            S_CheckBox {
                anchors.centerIn: parent
                checked: rowModel.checked
                Layout.alignment: Qt.AlignVCenter
                onCheckedChanged: {
                    tableView.model.checkRow(row, checked)
                    if (checked === force_table.selectAll[options.table_key])
                        return
                    Qt.callLater(() => {
                                     force_table.selectAll[options.table_key]
                                     = tableView.model.selectionModel(
                                         ).length === tableView.model.dataSourceSize
                                     force_table.selectAllChanged()
                                 })
                }
            }
        }
    }

    Component {
        id: com_display_format

        ComboBox {
            property var rowData: tableView.getRow(row)

            anchors.fill: parent
            model: ["十进制", "十六进制", "二进制"]
            font.pixelSize: 14
            currentIndex: model.findIndex(dataType => {
                                              return dataType === display
                                          })

            onActiveFocusChanged: {
                if (!activeFocus)
                    tableView.closeEditor()
            }

            onActivated: {
                const params = {
                    "id": rowData._key,
                    "deviceName": force_table.deviceName,
                    "tableName": rowData.tableName,
                    "varId": rowData.varId,
                    "forceName": rowData.forceName,
                    "monitoredValue": rowData.monitoredValue,
                    "displayFormat": currentValue,
                    "monitoringWithTrigger": rowData.monitoringWithTrigger,
                    "forcedValue": rowData.forcedValue,
                    "isForced": rowData.isForced,
                    "description": rowData.description,
                    "state": rowData.state
                }
                if (modifyVariable(params)) {
                    editTextChaged(currentValue)
                    tableView.closeEditor()
                } else {
                    message_dialog.show("修改失败")
                }
            }
        }
    }

    Component {
        id: com_trigger

        ComboBox {
            property var rowData: tableView.getRow(row)

            anchors.fill: parent
            model: force_table.isTrigger
            font.pixelSize: 14
            currentIndex: force_table.isTrigger.findIndex(type => {
                                                              return type === display
                                                          })

            onActiveFocusChanged: {
                if (!activeFocus)
                    tableView.closeEditor()
            }

            onActivated: {
                const params = {
                    "id": rowData._key,
                    "deviceName": force_table.deviceName,
                    "tableName": rowData.tableName,
                    "varId": rowData.varId,
                    "forceName": rowData.forceName,
                    "monitoredValue": rowData.monitoredValue,
                    "displayFormat": rowData.displayFormat,
                    "monitoringWithTrigger": currentValue,
                    "forcedValue": rowData.forcedValue,
                    "isForced": rowData.isForced,
                    "description": rowData.description,
                    "state": rowData.state
                }
                if (modifyVariable(params)) {
                    editTextChaged(currentValue)
                    tableView.closeEditor()
                } else {
                    message_dialog.show("修改失败")
                }
            }
        }
    }

    Component {
        id: com_forced_value

        TextField {
            property var rowData: tableView.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14

            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.forcedValue !== text) {
                        const params = {
                            "id": rowData._key,
                            "deviceName": force_table.deviceName,
                            "tableName": rowData.tableName,
                            "varId": rowData.varId,
                            "forceName": rowData.forceName,
                            "monitoredValue": rowData.monitoredValue,
                            "displayFormat": rowData.displayFormat,
                            "monitoringWithTrigger": rowData.monitoringWithTrigger,
                            "forcedValue": text,
                            "isForced": rowData.isForced,
                            "description": rowData.description,
                            "state": rowData.state
                        }
                        if (modifyVariable(params)) {
                            editTextChaged(text)
                        } else {
                            message_dialog.show("修改失败")
                        }
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: com_description

        TextField {
            property var rowData: tableView.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14

            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.description !== text) {
                        const params = {
                            "id": rowData._key,
                            "deviceName": force_table.deviceName,
                            "tableName": rowData.tableName,
                            "varId": rowData.varId,
                            "forceName": rowData.forceName,
                            "monitoredValue": rowData.monitoredValue,
                            "displayFormat": rowData.displayFormat,
                            "monitoringWithTrigger": rowData.monitoringWithTrigger,
                            "forcedValue": rowData.forcedValue,
                            "isForced": rowData.isForced,
                            "description": text,
                            "state": rowData.state
                        }
                        if (modifyVariable(params)) {
                            editTextChaged(text)
                        } else {
                            message_dialog.show("修改失败")
                        }
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    // 添加强制表
    Component {
        id: add_table_item
        Rectangle {
            width: 400
            height: 170

            QkButtonRow {
                id: title
                z: 10
                QkLabel {
                    anchors.left: parent.left
                    anchors.leftMargin: 10

                    anchors.verticalCenter: parent.verticalCenter
                    text: qsTr("Add table") + (trans ? trans.transString : "")
                }
            }

            ColumnLayout {
                anchors.top: title.bottom
                anchors.left: parent.left
                width: parent.width
                height: parent.height - title.height
                spacing: 10

                // 输入区域
                RowLayout {
                    Layout.fillWidth: true
                    Layout.margins: 20
                    spacing: 15

                    QkLabel {
                        text: qsTr("表 名:")
                        Layout.alignment: Qt.AlignVCenter
                        Layout.preferredWidth: 60 // 固定标签宽度
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignRight
                    }

                    QkTextField {
                        id: tableName
                        Layout.fillWidth: true
                        Layout.alignment: Qt.AlignVCenter
                        text: ""
                        selectByMouse: true // 启用文本选择
                        maximumLength: 30
                    }
                }

                // 修改后的按钮区域
                RowLayout {
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignBottom
                    Layout.bottomMargin: 20
                    spacing: 20

                    // 左侧占位弹簧
                    Item {
                        Layout.fillWidth: true
                    }

                    // 关闭按钮
                    QkButton {
                        text: qsTr("Close") + (trans ? trans.transString : "")
                        Layout.preferredWidth: 100
                        Layout.rightMargin: 50
                        onClicked: {
                            popup.close()
                        }
                    }

                    // 确认按钮
                    QkButton {
                        isGradientBgColor: true
                        text: qsTr("Confirm") + (trans ? trans.transString : "")
                        Layout.preferredWidth: 100
                        Layout.leftMargin: 50
                        onClicked: {
                            if (tableName.text.trim()) {
                                for (var i = 0; i < tabs.model.count; i++) {
                                    if (tabs.model.get(i).tab.trim(
                                                ) === tableName.text.trim()) {
                                        messageDialog.show("存在重名文件")
                                        return
                                    }
                                }

                                tabs.addPage(tree_table_component, {
                                                 "key": tableName.text.trim(),
                                                 "tab": tableName.text.trim(),
                                                 "tableData": []
                                             })

                                popup.close()
                            }
                        }
                    }

                    // 右侧占位弹簧
                    Item {
                        Layout.fillWidth: true
                    }
                }
            }
        }
    }

    MainPopup {
        id: popup
        anchors.centerIn: parent
    }

    function modifyVariable(params) {
        return VariableManage.modifyForcedVariable(
                    params.id, params.deviceName,
                    params.tableName, params.varId,
                    params.forceName, params.monitoredValue,
                    params.displayFormat, force_table.isTrigger.findIndex(
                        type => type === params.monitoringWithTrigger),
                    params.forcedValue, params.isForced,
                    params.description, params.state)
    }

    function customItem(comId, options = {}) {
        return {
            "comId": comId,
            "options": options
        }
    }

    function loadVarData(data) {
        return data.map(item => {
                            return {
                                "_key": item.id,
                                "isChecked": customItem(com_checkbox, {
                                                            "table_key": item.tableName
                                                        }),
                                "dataType": item.dataType,
                                "dataTypeID": item.dataTypeID,
                                "description": item.description,
                                "displayFormat": item.displayFormat,
                                "forceName": item.forceName,
                                "forcedValue": item.forcedValue,
                                "isForced": item.isForced,
                                "monitoredValue": item.monitoredValue,
                                "monitoringWithTrigger": force_table.isTrigger[item.monitoringWithTrigger],
                                "owned": item.owned,
                                "scope": item.scope,
                                "settingName": item.settingName,
                                "state": item.state,
                                "tableName": item.tableName,
                                "type": item.type,
                                "varId": item.varID
                            }
                        })
    }

    function addVariable(list) {
        let flag = true
        list.forEach(item => {
                         flag = flag && VariableManage.addForcedVariable(
                             force_table.deviceName, tabs.activeKey,
                             Number(item.id), item.name, "", "十进制", 0, "", 1,
                             item.description, item.state,
                             item.scope, item.owned, item.type, item.dataType,
                             Number(item.dataTypeID))
                     })
        if (flag) {
            let tableData = VariableManage.getForcedList(
                    force_table.deviceName, tabs.activeKey)
            tableData = loadVarData(tableData)
            tabs.findPage(tabs.activeKey).table_view.dataSource = tableData
            force_table.selectAll[tabs.activeKey] = false
        } else {
            messageDialog.show("添加变量失败")
        }
    }

    function getDataBind() {
        tabs.model.clear()
        const tableNameList = VariableManage.getForcedTableName(
                                force_table.deviceName)
        tableNameList.forEach(item => {
                                  let tableData = VariableManage.getForcedList(
                                      force_table.deviceName, item)
                                  tableData = loadVarData(tableData)

                                  force_table.selectAll[item] = false
                                  tabs.addPage(tree_table_component, {
                                                   "key": item,
                                                   "tab": item,
                                                   "tableData": tableData
                                               })
                              })
    }
}
