﻿#include <QDebug>
#include <QDir>
#include <QCoreApplication>
#include <QJsonParseError>
#include <QJsonDocument>
#include <QtMath>
#include "commonobject.h"
#include "commonmanage.h"
#include "ccompatibletype.h"


CommonManage &CommonManage::instance()
{
    static CommonManage instance;
    return instance;
}

void CommonManage::init(const QString &appPath)
{
    appDir = appPath;
    //读取数据类型兼容列表
    //CCompatibleTypeCFC::instance()->readFile(appDir);
}

void CommonManage::OpenProject(const QString &pName, const QString &pPath)
{
    projectName = pName;
    projectDir = pPath;

    CommonFileList.clear();
    //读取功能块列表
}

//关闭当前项目
void CommonManage::CloseProject()
{
    projectName.clear();
    projectDir.clear();
    CommonFileList.clear();
    //清除功能块列表
}

QString CommonManage::initFile(const QString &fileName, const QString &fileType, const QString &codeType)
{
    QDomDocument doc;
    // 写入xml头部
    QDomProcessingInstruction instruction; // 添加处理命令
    instruction = doc.createProcessingInstruction("xml", "version=\"1.0\" encoding=\"UTF-8\"");
    doc.appendChild(instruction);
    // 添加根节点
    QDomElement root = doc.createElement("File");
    root.setAttribute("Name", fileName);
    root.setAttribute("Code", codeType);
    root.setAttribute("Type", fileType);
    root.setAttribute("Version", "1.0.0");
    root.setAttribute("WidthNumber", "100");
    root.setAttribute("HeightNumber", "100");
    root.setAttribute("Author", QString());
    root.setAttribute("CreatedOn", QDate::currentDate().toString("yyyy/MM/dd"));
    root.setAttribute("LastChange", QDate::currentDate().toString("yyyy/MM/dd"));
    root.setAttribute("Comment", QString());
    root.setAttribute("TasksName", "T2");

    //表
    QDomElement networks = doc.createElement("Networks");
    QDomElement network = doc.createElement("Network");
    network.setAttribute("Type", "Network");
    network.setAttribute("Number", "0");
    network.setAttribute("Enable", "1");
    network.setAttribute("Label", "");
    networks.appendChild(network);
    root.appendChild(networks);
    //接口
    QDomElement interface = doc.createElement("Interface");
    QDomElement inputv = doc.createElement("InputVariables");
    QDomElement outputv = doc.createElement("OutputVariables");
    interface.appendChild(inputv);
    interface.appendChild(outputv);
    root.appendChild(interface);

    doc.appendChild(root);
    // 将doc转化为字符串
    return doc.toString();
}

bool CommonManage::readFile(const QString &deviceName, const QString &fileType, const QString &fileName, const QString &filePath)
{
    if (projectDir.isEmpty())
    {
        qDebug() << "newfbd editor projectDir.isEmpty";
        return false;
    }
    else
    {
        //根据项目路径和fileType
        qDebug() << "ReadFile FBD filePath" << filePath;
        //判断文件是否存在
        QFileInfo fileInfo(filePath);
        if (!fileInfo.isFile())
        {
            qDebug() << "filePath is emplty";
            return false;
        }
        else
        {
            QSharedPointer<CommonFile> newFile = QSharedPointer<CommonFile>(new CommonFile());
            newFile->fromXml(filePath);
            newFile->filePath = filePath;
            newFile->compliePath = filePath;
            newFile->compliePath = newFile->compliePath.replace("/User/", "/" + deviceName + "/");
            qDebug() << "ReadFile check graphic file Read Ok:" << newFile->Name << newFile->compliePath;
            CommonFileList.insert(filePath, newFile);
            //处理元素引脚收缩问题
            putVarComponentInOrder(filePath);
            //如果一个网络都没有则添加一个网络
            if (newFile->networks->networkMap.size() == 0)
            {
                addNetwork(filePath, 0);
            }
            qDebug() << "FileList count:" << CommonFileList.count();
        }
    }
    return true;
}

// 读取加密文件
bool CommonManage::readFile(const QString &deviceName, const QString &fileType,
                            const QString &fileName, const QString &filePath,
                            const QByteArray &bytes)
{
    QSharedPointer<CommonFile> newFile = QSharedPointer<CommonFile>(new CommonFile());
    newFile->fromXml(bytes);
    newFile->filePath = filePath;
    newFile->compliePath = filePath;
    newFile->compliePath = newFile->compliePath.replace("/User/", "/" + deviceName + "/");
    qDebug() << "ReadFile check graphic file Read Ok:" << newFile->Name << newFile->compliePath;
    CommonFileList.insert(filePath, newFile);
    qDebug() << "FileList count:" << CommonFileList.count();

    return true;
}

CommonFile *CommonManage::getFile(QString absolutepath)
{
    CommonFile *newFile = new CommonFile();
    QFileInfo fileInfo(absolutepath);
    if (fileInfo.isFile())
    {
        newFile->fromXml(absolutepath);
        newFile->filePath = absolutepath;
    }
    return newFile;
}

CommonFile *CommonManage::getFile(QString absolutepath, const QByteArray &bytes)
{
    CommonFile *newFile = new CommonFile();
    QFileInfo fileInfo(absolutepath);
    if (fileInfo.isFile())
    {
        newFile->fromXml(bytes);
        newFile->filePath = absolutepath;
    }
    return newFile;
}

QByteArray CommonManage::getFileData(const QString &fileKey)
{
    QByteArray bytes;
    if (CommonFileList.contains(fileKey))
    {
        CommonFile *fbd = CommonFileList[fileKey].data();
        fbd->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        bytes = fbd->toXml();
    }
    return bytes;
}

bool CommonManage::saveFile(const QString &fileKey)
{
    if (CommonFileList.contains(fileKey))
    {
        putStartOnEN(fileKey);

        cleanFailedVariableComponent(fileKey);

        CommonFile *fbd = CommonFileList[fileKey].data();
        fbd->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        if (fbd->Code == "CFC")
        {
            fbd->TasksName = "";
        }
        fbd->toXml(fileKey);
        return true;
    }
    return false;
}

void CommonManage::saveAllFile()
{
    QMap<QString, QSharedPointer<CommonFile>>::Iterator iter;
    for (iter = CommonFileList.begin(); iter != CommonFileList.end(); ++iter)
    {
        CommonFile *fbd = iter.value().data();
        fbd->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        fbd->toXml(fbd->filePath);
    }
}

QJsonObject CommonManage::getPlanInfo(const QString &fileKey)
{
    qDebug() << "CommonManage::getPlanInfo CommonFileList.size" << CommonFileList.size();
    for (auto &key : CommonFileList.keys())
    {
        qDebug() << "CommonManage::getPlanInfo" << key;
    }

    QJsonObject obj;
    if (CommonFileList.contains(fileKey))
    {
        CommonFile *fbd = CommonFileList[fileKey].data();
        obj = fbd->toJsonObject();
    }
    return obj;
}

void CommonManage::modifyTaskName(const QString &fileKey, const QString taskName)
{
    qDebug() << "modifyTaskName" << fileKey << taskName;
    //已在内存中
    if (CommonFileList.contains(fileKey))
    {
        CommonFile *fbd = CommonFileList[fileKey].data();
        if (fbd->Code != "CFC")
        {
            for (auto &com : fbd->components->componentMap)
            {
                if (com->Type == "Block")
                {
                    com->TaskName = taskName;
                }
            }
        }
        //保存文件
        fbd->TasksName = taskName;
        fbd->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        fbd->toXml(fileKey);
    }
    else
    {
        //读文件到内存中然后再修改保存
        QSharedPointer<CommonFile> newFile = QSharedPointer<CommonFile>(new CommonFile());
        newFile->fromXml(fileKey);
        newFile->filePath = fileKey;
        newFile->compliePath = fileKey;
        if (newFile->Code != "CFC")
        {
            for (auto &com : newFile->components->componentMap)
            {
                if (com->Type == "Block")
                {
                    com->TaskName = taskName;
                }
            }
        }
        //保存文件
        newFile->TasksName = taskName;
        newFile->LastChange = QDate::currentDate().toString("yyyy/MM/dd");
        newFile->toXml(fileKey);
    }
}

void CommonManage::updateAuthorComment(const QString &fileKey, const QString &author, const QString &comment)
{
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        fbd->Author = author;
        fbd->Comment = comment;
        //更新文件吗？
        //cfcFile->WriteFile();
    }
}

QJsonArray CommonManage::getNetwork(const QString &fileKey)
{
    QJsonArray ary;
    if (CommonFileList.contains(fileKey))
    {
        //取出文件
        CommonFile *fbd = CommonFileList[fileKey].data();
        //qDebug() << "fbd->networks.networkList.size():" << fbd->networks.networkList.size();
        //获取所有网络
        ary = fbd->networks->toJsonArray();
    }
    else
    {
        qDebug() << "miss fileKey:" << fileKey;
    }
    return ary;
}

QJsonArray CommonManage::getComponentConnector(const QString &fileKey, int networkNumber)
{
    QJsonArray ary;
    if (CommonFileList.contains(fileKey))
    {
        //取出文件
        CommonFile *fbd = CommonFileList[fileKey].data();
        //qDebug() << "fbd->networks.networkList.size():" << fbd->networks.networkList.size();
        //获取所有网络
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            QSharedPointer<Network> network = fbd->networks->networkMap.value(networkNumber);
            //网络的元件
            QMap<int, QSharedPointer<Component>>::iterator itor;
            for (itor = network->componentMap.begin(); itor != network->componentMap.end(); itor++)
            {
                //包括引脚
                QJsonObject objblock = itor.value()->toJsonObject();

                ary.append(objblock);
            }
        }
    }
    else
    {
        qDebug() << "miss fileKey:" << fileKey;
    }
    return ary;
}

QJsonArray CommonManage::getNetworkConnections(const QString &fileKey, int networkNumber)
{
    QJsonArray ary;
    if (CommonFileList.contains(fileKey))
    {
        //取出文件
        CommonFile *fbd = CommonFileList[fileKey].data();
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            QSharedPointer<Network> network = fbd->networks->networkMap.value(networkNumber);
            //判断链接所属 根据源和目的块的名称 分配到每个网络上
            for (QSharedPointer<CommonConnection> &conn : fbd->connections->connectionList)
            {
                QMap<int, QSharedPointer<Component>>::iterator itor;
                for (itor = network->componentMap.begin(); itor != network->componentMap.end(); itor++)
                {
                    //按源来索引
                    if (conn->SourceComponentNumber == itor.value()->Number)
                    {
                        QJsonObject objconnection = conn->toJsonObject();
                        ary.append(objconnection);
                    }
                }
            }
        }
    }
    else
    {
        qDebug() << "miss fileKey:" << fileKey;
    }
    return ary;
}

QJsonArray CommonManage::getAllBlockName(const QString &fileKey)
{
    QJsonArray objArr;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        for (auto &com : fbd->components->componentMap)
        {
            if (com->Type == "Block")
            {
                QJsonObject obj;
                obj["Name"] = com->Name;
                obj["Number"] = com->Number;
                obj["ChildType"] = com->ChildType;
                obj["TaskName"] = com->TaskName;
                obj["TaskOrderNumber"] = com->TaskOrderNumber;
                obj["AuxContent"] = com->AuxContent;

                objArr.append(obj);
            }
        }
    }
    return objArr;
}

bool CommonManage::addNetwork(const QString &fileKey, int number)
{

    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        //qDebug() << "addNetwork" << fbd->networks->networkMap.size() << number;
        if (number >= fbd->networks->networkMap.size())
        {
            //最后新增
            QSharedPointer<Network> newnw = QSharedPointer<Network>(new Network());
            newnw->Number = fbd->networks->networkMap.size();
            newnw->Type = "";
            newnw->Enable = true;
            newnw->Label = "";
            fbd->networks->networkMap.insert(newnw->Number, newnw);
        }
        else
        {
            //中间插入
            for (QSharedPointer<Network> &nw : fbd->networks->networkMap)
            {
                if (number <= nw->Number)
                {
                    nw->Number = nw->Number + 1;

                    for (QSharedPointer<Component> &com : nw->componentMap)
                    {
                        if (com->network != nullptr)
                        {
                            //对应到新的NetworkNumber
                            com->NetworkNumber = com->network->Number;
                        }
                    }
                }
            }

            //重排页码序号
            fbd->networks->sortNetworkList();
            //插入网络，调整元件对应的NetworkNumber

            QSharedPointer<Network> newnw = QSharedPointer<Network>(new Network());
            newnw->Number = number;
            newnw->Type = "";
            newnw->Enable = true;
            newnw->Label = "";

            fbd->networks->networkMap.insert(number, newnw);
        }
        fbd->reSize();

        flag = true;
        emit fileChanged(fileKey);

        //为一个网络添加默认启动常量元件
        //putStartOnEN(fileKey);
    }
    return flag;
}

bool CommonManage::deleteNetwork(const QString &fileKey, int number)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        //找到对应网络
        if (fbd->networks->networkMap.contains(number))
        {
            for (auto nw : fbd->networks->networkMap)
            {
                if (nw->Number == number)
                {
                    QSharedPointer<Network> nw = fbd->networks->networkMap.value(number);
                    //删除元件
                    for (auto &com : fbd->components->componentMap)
                    {
                        //找到所含元件
                        if (com->NetworkNumber == nw->Number)
                        {
                            //删除元件
                            deleteComponent(fileKey, com->Number);
                        }
                    }
                }
                else if (nw->Number > number)
                {
                    nw->Number = nw->Number - 1;
                }
            }
            //删除网络
            fbd->networks->networkMap.remove(number);
            //重排页码序号
            fbd->networks->sortNetworkList();
            //插入网络，调整元件对应的NetworkNumber
            for (QSharedPointer<Component> &com : fbd->components->componentMap)
            {
                if (com->network != nullptr)
                {
                    //对应到新的NetworkNumber
                    com->NetworkNumber = com->network->Number;
                }
            }
            flag = true;
            emit fileChanged(fileKey);
        }
    }
    return flag;
}

bool CommonManage::modifyNetworkLabel(const QString &fileKey, int number, const QString &newLabel)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        //查找是否有重名的
        bool findSame = false;
        for (QSharedPointer<Network> &nw : fbd->networks->networkMap)
        {
            //找到对应网络
            if (number != nw->Number && nw->Label == newLabel)
            {
                findSame = true;
                break;
            }
        }
        if (!findSame)
        {
            for (QSharedPointer<Network> &nw : fbd->networks->networkMap)
            {
                //找到对应网络
                if (number == nw->Number)
                {
                    nw->Label = newLabel;

                    flag = true;

                    QJsonArray ary;
                    QJsonObject obj;
                    obj["action"] = "modify";
                    obj["name"] = "Label";
                    obj["value"] = nw->Label;

                    ary.append(obj);
                    emit networkChanged(fileKey, number, ary);
                    break;
                }
            }
        }
    }
    return flag;
}

bool CommonManage::modifyNetworkEnable(const QString &fileKey, int number, const bool enable)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        //找到对应网络
        if (fbd->networks->networkMap.contains(number))
        {
            QSharedPointer<Network> nw = fbd->networks->networkMap.value(number);
            nw->Enable = enable;

            //所属元件Enable同步
            QMap<int, QSharedPointer<Component>>::iterator itor;
            for (itor = nw->componentMap.begin(); itor != nw->componentMap.end(); itor++)

            {
                itor.value()->Enable = nw->Enable;
            }

            flag = true;

            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "modify";
            obj["name"] = "Enable";
            obj["value"] = nw->Enable;

            ary.append(obj);
            emit networkChanged(fileKey, number, ary);
        }
    }
    return flag;
}

bool CommonManage::modifyComponentName(const QString &fileKey, int compNumber, const QString &newName)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);
            //查找非自己的重名
            QSharedPointer<Component> searchCom = fbd->searchComponentFromName(newName);
            if (searchCom == nullptr)
            {
                com->Name = newName;

                flag = true;
                QJsonArray ary;
                QJsonObject obj;
                obj["action"] = "modify";
                obj["name"] = "Name";
                obj["value"] = newName;

                ary.append(obj);
                emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);
            }
            else if (searchCom == com)
            {
                com->Name = newName;
                flag = true;
            }
        }
    }
    return flag;
}

bool CommonManage::modifyComponentComment(const QString &fileKey, int compNumber, const QString &comment)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);
            com->Comment = comment;
            flag = true;
            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "modify";
            obj["name"] = "Comment";
            obj["value"] = comment;

            ary.append(obj);
            emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);
        }
    }
    return flag;
}

bool CommonManage::deleteComponent(const QString &fileKey, int compNumber)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        qDebug() << "deleteComponent" << fbd->components->componentMap.size();
        QList<int> toDeleteNetworkNumbers;
        QList<int> toDeleteCompNumbers;
        for (auto &com : fbd->components->componentMap)
        {
            qDebug() << "deleteComponent tj" << compNumber << com->Number << com->ParentNumber;
            //删除指定元件，以及该元件的子元件
            if (com->Number == compNumber || com->ParentNumber == compNumber)
            {
                //删除链接 todo
                QJsonArray aryLine;

                for (int l = 0; l < fbd->connections->connectionList.size(); l++)
                {
                    QSharedPointer<CommonConnection> conn = fbd->connections->connectionList.at(l);
                    if (conn->SourceComponentNumber == compNumber || conn->TargetComponentNumber == compNumber)
                    {
                        //记录删除的链接
                        QJsonObject objLine;
                        objLine["action"] = "delete";
                        objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                        objLine["SourcePinId"] = conn->SourcePinId;
                        objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                        objLine["TargetPinId"] = conn->TargetPinId;
                        aryLine.append(objLine);

                        fbd->connections->connectionList.removeAt(l);
                        l--;
                    }
                }
                //删除元件
                int networknumber = com->NetworkNumber;
                int compnumber = com->Number;
                qDebug() << "deleteComponent del comp" << networknumber << compnumber;
                toDeleteNetworkNumbers.append(networknumber);
                toDeleteCompNumbers.append(compnumber);
                //删除network中对应关系
                qDebug() << "deleteComponent delete from network->componentMap" << networknumber << compnumber;
                if (fbd->networks->networkMap.contains(networknumber))
                {
                    if (fbd->networks->networkMap.value(networknumber)->componentMap.contains(compnumber))
                    {
                        fbd->networks->networkMap.value(networknumber)->componentMap.remove(compnumber);
                    }
                }

                flag = true;


                if (aryLine.count() > 0)
                {
                    emit lineChanged(fileKey, networknumber, aryLine);

                    qDebug() << "deleteComponent aryLine" << aryLine;
                }
            }
        }
        qDebug() << "deleteComponent" << toDeleteNetworkNumbers << toDeleteCompNumbers;
        for (int i = 0; i < toDeleteNetworkNumbers.size(); i++)
        {
            int networknumber = toDeleteNetworkNumbers.at(i);
            int compnumber = toDeleteCompNumbers.at(i);

            fbd->components->componentMap.remove(compnumber);

            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "delete";
            ary.append(obj);
            emit componentChanged(fileKey, networknumber, compnumber, ary);
            qDebug() << "deleteComponent ary" << networknumber << compnumber << ary;
        }
    }
    return flag;
}

bool CommonManage::deleteComponentFromParent(const QString &fileKey, int parentCompNumber)
{
    qDebug() << "deleteComponentFromParent" << fileKey << parentCompNumber;
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        QList<int> toDeleteNetworkNumbers;
        QList<int> toDeleteCompNumbers;
        for (auto &com : fbd->components->componentMap)
        {
            //删除指定元件，以及该元件的子元件
            if (com->ParentNumber == parentCompNumber)
            {
                //删除链接 todo
                QJsonArray aryLine;

                for (int l = 0; l < fbd->connections->connectionList.size(); l++)
                {
                    QSharedPointer<CommonConnection> conn = fbd->connections->connectionList.at(l);
                    if (conn->SourceComponentNumber == com->Number || conn->TargetComponentNumber == com->Number)
                    {
                        //记录删除的链接
                        QJsonObject objLine;
                        objLine["action"] = "delete";
                        objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                        objLine["SourcePinId"] = conn->SourcePinId;
                        objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                        objLine["TargetPinId"] = conn->TargetPinId;
                        aryLine.append(objLine);

                        fbd->connections->connectionList.removeAt(l);
                        l--;
                    }
                }

                int networkNumber = com->NetworkNumber;
                int comNumber = com->Number;
                toDeleteNetworkNumbers.append(networkNumber);
                toDeleteCompNumbers.append(comNumber);
                //删除network中对应关系
                if (fbd->networks->networkMap.contains(networkNumber))
                {
                    if (fbd->networks->networkMap.value(networkNumber)->componentMap.contains(comNumber))
                    {
                        fbd->networks->networkMap.value(networkNumber)->componentMap.remove(comNumber);
                    }
                }

                flag = true;

                if (aryLine.count() > 0)
                {
                    emit lineChanged(fileKey, networkNumber, aryLine);
                }
            }
        }
        qDebug() << "deleteComponent" << toDeleteNetworkNumbers << toDeleteCompNumbers;
        for (int i = 0; i < toDeleteNetworkNumbers.size(); i++)
        {
            int networknumber = toDeleteNetworkNumbers.at(i);
            int compnumber = toDeleteCompNumbers.at(i);

            fbd->components->componentMap.remove(compnumber);

            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "delete";
            ary.append(obj);
            emit componentChanged(fileKey, networknumber, compnumber, ary);
            qDebug() << "deleteComponent ary" << networknumber << compnumber << ary;
        }

    }
    return flag;
}

bool CommonManage::deleteComponentFromParent(const QString &fileKey, int parentCompNumber, int parentPinId)
{
    qDebug() << "deleteComponentFromParent" << fileKey << parentCompNumber << parentPinId;
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        QList<int> toDeleteNetworkNumbers;
        QList<int> toDeleteCompNumbers;
        for (auto &com : fbd->components->componentMap)
        {
            //删除该元件的子元件
            if (com->ParentNumber == parentCompNumber && com->ParentPinId == parentPinId)
            {
                //删除链接 todo
                QJsonArray aryLine;

                for (int l = 0; l < fbd->connections->connectionList.size(); l++)
                {
                    QSharedPointer<CommonConnection> conn = fbd->connections->connectionList.at(l);
                    if (conn->SourceComponentNumber == com->Number || conn->TargetComponentNumber == com->Number)
                    {
                        //记录删除的链接
                        QJsonObject objLine;
                        objLine["action"] = "delete";
                        objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                        objLine["SourcePinId"] = conn->SourcePinId;
                        objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                        objLine["TargetPinId"] = conn->TargetPinId;
                        aryLine.append(objLine);

                        fbd->connections->connectionList.removeAt(l);
                        l--;
                    }
                }

                int networkNumber = com->NetworkNumber;
                int comNumber = com->Number;
                toDeleteNetworkNumbers.append(networkNumber);
                toDeleteCompNumbers.append(comNumber);
                //删除network中对应关系
                if (fbd->networks->networkMap.contains(networkNumber))
                {
                    if (fbd->networks->networkMap.value(networkNumber)->componentMap.contains(comNumber))
                    {
                        fbd->networks->networkMap.value(networkNumber)->componentMap.remove(comNumber);
                    }
                }
                flag = true;
                QJsonArray ary;
                if (aryLine.count() > 0)
                {
                    emit lineChanged(fileKey, networkNumber, aryLine);
                }

                //将该引脚上的对应ChildNumber置-1
                for (QSharedPointer<Component> &fcom : fbd->components->componentMap)
                {
                    if (fcom->Number == parentCompNumber)
                    {
                        for (QSharedPointer<Connector> &fconn : fcom->connectorList)
                        {
                            if (fconn->PinId == parentPinId)
                            {
                                fconn->ChildNumber = -1;
                                break;
                            }
                        }
                        break;
                    }
                }

            }
        }
        qDebug() << "deleteComponent" << toDeleteNetworkNumbers << toDeleteCompNumbers;
        for (int i = 0; i < toDeleteNetworkNumbers.size(); i++)
        {
            int networknumber = toDeleteNetworkNumbers.at(i);
            int compnumber = toDeleteCompNumbers.at(i);

            fbd->components->componentMap.remove(compnumber);

            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "delete";
            ary.append(obj);
            emit componentChanged(fileKey, networknumber, compnumber, ary);
            qDebug() << "deleteComponent ary" << networknumber << compnumber << ary;
        }
    }
    return flag;
}


bool CommonManage::adjustmentComponentConnectorPath(const QString &fileKey, int compNumber)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);
            QJsonArray aryLine;
            //遍历每个引脚
            for (QSharedPointer<Connector> &cor : com->connectorList)
            {
                //遍历所有链接
                for (QSharedPointer<CommonConnection> &conn : fbd->connections->connectionList)
                {
                    //源
                    if (conn->SourceComponentNumber == com->Number && conn->SourcePinId == cor->PinId)
                    {
                        int startX = cor->XPos + com->XPos;
                        int startY = cor->YPos + com->YPos;
                        QJsonParseError error;
                        QJsonDocument doucment = QJsonDocument::fromJson(conn->PlotPath.toUtf8(), &error);
                        if (!doucment.isNull() && (error.error == QJsonParseError::NoError))
                        {
                            if (doucment.isArray())
                            {
                                QJsonArray array = doucment.array();
                                QJsonObject obj = array.last().toObject();

                                int endX = obj["x"].toInt();
                                int endY = obj["y"].toInt();
                                int xLength = qAbs(startX - endX);
                                int yLength = qAbs(startY - endY);
                                conn->Length = xLength + yLength;
                                conn->PlotPath = calculatePath(startX, startY, endX, endY, conn->Length);
                            }
                        }
                        QJsonObject objLine;
                        objLine["action"] = "modify";
                        objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                        objLine["SourcePinId"] = conn->SourcePinId;
                        objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                        objLine["TargetPinId"] = conn->TargetPinId;
                        objLine["name"] = "PlotPath";
                        objLine["value"] = conn->PlotPath;
                        aryLine.append(objLine);

                        QJsonObject objLine1;
                        objLine1["action"] = "modify";
                        objLine1["SourceComponentNumber"] = conn->SourceComponentNumber;
                        objLine1["SourcePinId"] = conn->SourcePinId;
                        objLine1["TargetComponentNumber"] = conn->TargetComponentNumber;
                        objLine1["TargetPinId"] = conn->TargetPinId;
                        objLine1["name"] = "Length";
                        objLine1["value"] = conn->Length;
                        aryLine.append(objLine1);
                    }
                    //目标
                    if (conn->TargetComponentNumber == com->Number && conn->TargetPinId == cor->PinId)
                    {
                        int endX = cor->XPos + com->XPos;
                        int endY = cor->YPos + com->YPos;
                        QJsonParseError error;
                        QJsonDocument doucment = QJsonDocument::fromJson(conn->PlotPath.toUtf8(), &error);
                        if (!doucment.isNull() && (error.error == QJsonParseError::NoError))
                        {
                            if (doucment.isArray())
                            {
                                QJsonArray array = doucment.array();
                                QJsonObject obj = array.first().toObject();

                                int startX = obj["x"].toInt();
                                int startY = obj["y"].toInt();
                                int xLength = qAbs(startX - endX);
                                int yLength = qAbs(startY - endY);
                                conn->Length = xLength + yLength;
                                conn->PlotPath = calculatePath(startX, startY, endX, endY, conn->Length);
                            }
                        }
                        QJsonObject objLine;
                        objLine["action"] = "modify";
                        objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                        objLine["SourcePinId"] = conn->SourcePinId;
                        objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                        objLine["TargetPinId"] = conn->TargetPinId;
                        objLine["name"] = "PlotPath";
                        objLine["value"] = conn->PlotPath;
                        aryLine.append(objLine);

                        QJsonObject objLine1;
                        objLine1["action"] = "modify";
                        objLine1["SourceComponentNumber"] = conn->SourceComponentNumber;
                        objLine1["SourcePinId"] = conn->SourcePinId;
                        objLine1["TargetComponentNumber"] = conn->TargetComponentNumber;
                        objLine1["TargetPinId"] = conn->TargetPinId;
                        objLine1["name"] = "Length";
                        objLine1["value"] = conn->Length;
                        aryLine.append(objLine1);
                    }
                }
            }

            //信号通知页面更新连线
            if (aryLine.count() > 0)
            {
                emit lineChanged(fileKey, com->NetworkNumber, aryLine);
            }
            flag = true;
        }
    }

    return flag;
}

void CommonManage::autoConnectWithVariableAndBlock(const QString &fileKey, QSharedPointer<Component> &varComponent)
{
    //轮询所有相同网络下的block元件，找到计算距离为《=1的，尝试进行建立链接
    QMapIterator<int, QSharedPointer<Component>> itor(varComponent->network->componentMap);
    while (itor.hasNext())
    {
        itor.next();
        QSharedPointer<Component> com = itor.value();
        if (com->Type == "Block")
        {
            for (QSharedPointer<Connector> &v_conn : varComponent->connectorList)
            {
                //变量元件引脚定位
                int v_x = varComponent->XPos + v_conn->XPos;
                int v_y = varComponent->YPos + v_conn->YPos;
                //轮询引脚
                for (QSharedPointer<Connector> &b_conn : com->connectorList)
                {
                    int b_x = com->XPos + b_conn->XPos;
                    int b_y = com->YPos + b_conn->YPos;

                    int len = qAbs(v_x - b_x) + qAbs(v_y - b_y);
                    if (len <= 1)
                    {
                        //尝试建立链接
                        QJsonObject firstConn;
                        firstConn["NetworkNumber"] = varComponent->NetworkNumber;
                        firstConn["ComponentNumber"] = varComponent->Number;
                        firstConn["ComponentInstanceName"] = varComponent->InstanceName;
                        firstConn["ComponentType"] = varComponent->Type;
                        firstConn["ComponentChildType"] = varComponent->ChildType;
                        firstConn["PinId"] = v_conn->PinId;
                        firstConn["ConnectorInstanceName"] = v_conn->InstanceName;
                        firstConn["Name"] = v_conn->Name;
                        firstConn["DataType"] = v_conn->DataType;
                        firstConn["Direction"] = v_conn->Direction;
                        firstConn["Path"] = varComponent->AuxContent;
                        firstConn["XPos"] = v_x;
                        firstConn["YPos"] = v_y;

                        QJsonObject secondConn;
                        secondConn["NetworkNumber"] = com->NetworkNumber;
                        secondConn["ComponentNumber"] = com->Number;
                        secondConn["ComponentInstanceName"] = com->InstanceName;
                        secondConn["ComponentType"] = com->Type;
                        secondConn["ComponentChildType"] = com->ChildType;
                        secondConn["PinId"] = b_conn->PinId;
                        secondConn["ConnectorInstanceName"] = b_conn->InstanceName;
                        secondConn["Name"] = b_conn->Name;
                        secondConn["DataType"] = b_conn->DataType;
                        secondConn["Direction"] = b_conn->Direction;
                        secondConn["Path"] = com->InstanceName + "." + b_conn->InstanceName;
                        secondConn["XPos"] = b_x;
                        secondConn["YPos"] = b_y;

                        addConnection(fileKey, firstConn, secondConn);
                    }
                }
            }
        }
    }
}

void CommonManage::followMovementWithBlock(const QString &fileKey, QSharedPointer<Component> &blockComponent, int xoffset,
                                           int yoffset)
{
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        //遍历所有引脚
        for (auto &conn : blockComponent->connectorList)
        {
            //有绑定的子元件
            if (conn->ChildNumber != -1)
            {
                if (fbd->components->componentMap.contains(conn->ChildNumber))
                {
                    QSharedPointer<Component> com = fbd->components->componentMap.value(conn->ChildNumber) ;
                    if (com->Type == "Variable" && com->ParentNumber == blockComponent->Number && com->ParentPinId == conn->PinId)
                    {
                        int newx = com->XPos + xoffset;
                        int newy = com->YPos + yoffset;
                        moveComponent(fileKey, com->Number, newx, newy);
                    }
                }
            }
        }



        //        for (QSharedPointer<CommonConnection> connection : fbd->connections->connectionList)
        //        {
        //            //距离为0的
        //            if (connection->SourceComponentNumber == blockComponent->Number && connection->Length < 3)
        //            {
        //                if (fbd->components->componentMap.contains(connection->TargetComponentNumber))
        //                {
        //                    QSharedPointer<Component> com = fbd->components->componentMap.value(connection->TargetComponentNumber);
        //                    if (com->Type == "Variable")
        //                    {
        //                        int newx = com->XPos + xoffset;
        //                        int newy = com->YPos + yoffset;
        //                        moveComponent(fileKey, com->Number, newx, newy);
        //                        break;
        //                    }
        //                }
        //            }
        //            else if (connection->TargetComponentNumber == blockComponent->Number && connection->Length < 3)
        //            {
        //                if (fbd->components->componentMap.contains(connection->SourceComponentNumber))
        //                {
        //                    QSharedPointer<Component> com = fbd->components->componentMap.value(connection->SourceComponentNumber);
        //                    if (com->Type == "Variable")
        //                    {
        //                        int newx = com->XPos + xoffset;
        //                        int newy = com->YPos + yoffset;
        //                        moveComponent(fileKey, com->Number, newx, newy);
        //                        break;
        //                    }
        //                }
        //            }
        //        }
    }
}

bool CommonManage::moveComponent(const QString &fileKey, int compNumber, int x, int y)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);
            int xoffset = x - com->XPos;
            int yoffset = y - com->YPos;
            if (xoffset != 0 || yoffset != 0)
            {
                com->XPos = x;
                com->YPos = y;

                flag = true;
                QJsonArray ary;
                QJsonObject obj;
                obj["action"] = "modify";
                obj["name"] = "XPos";
                obj["value"] = com->XPos;
                ary.append(obj);

                QJsonObject obj1;
                obj1["action"] = "modify";
                obj1["name"] = "YPos";
                obj1["value"] = com->YPos;
                ary.append(obj1);
                emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);

                //移动块元件的情况下 尝试移动跟随的变量组件
                if (com->Type == "Block")
                {
                    followMovementWithBlock(fileKey, com, xoffset, yoffset);
                }

                //自动调整引脚连接线
                //连线变化信号
                adjustmentComponentConnectorPath(fileKey, com->Number);
            }

        }
        if (flag)
        {
            fbd->reSize();
            if (fbd->Code == "FBD")
            {
                QJsonArray tnary = fbd->sortAllTaskOrderNumber();
                if (tnary.size() > 0)
                {
                    for (int i = 0; i < tnary.size(); i++)
                    {
                        QJsonObject obj = tnary.at(i).toObject();

                        QJsonArray newary;
                        newary.append(obj);

                        emit componentChanged(fileKey, obj["NetworkNumber"].toInt(), obj["ComponentNumber"].toInt(), newary);
                    }
                }
            }
        }
    }
    return flag;
}

bool CommonManage::resizeComponent(const QString &fileKey, int compNumber, int w, int h)
{
    qDebug() << fileKey << compNumber << w << h;
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);

            //最小宽度  和  最小高度
            int newWidth = (w < com->MinWidth ? com->MinWidth : w);
            int newHeight = (h < 1 ? 1 : h);
            int offsetWidth = com->Width - newWidth;
            int offsetHeight = com->Height - newHeight;
            com->Width = newWidth;
            com->Height = newHeight;
            com->setPinPosOffset();

            flag = true;
            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "modify";
            obj["name"] = "Width";
            obj["value"] = com->Width;
            ary.append(obj);

            QJsonObject obj1;
            obj1["action"] = "modify";
            obj1["name"] = "Height";
            obj1["value"] = com->Height;
            ary.append(obj1);
            emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);

            //自动调整引脚连接线
            //连线变化提交
            adjustmentComponentConnectorPath(fileKey, com->Number);

            //右侧变量元件位置发生变化
            if (offsetWidth != 0 || offsetHeight != 0)
            {
                for (QSharedPointer<Component> &childcom : fbd->components->componentMap)
                {
                    if (childcom->ParentNumber == com->Number && childcom->LeftOrRight == 2)
                    {
                        qDebug() << "offsetWidth offsetHeight" << childcom->XPos << offsetWidth <<  childcom->YPos << offsetHeight;
                        moveComponent(fileKey, childcom->Number, childcom->XPos - offsetWidth, childcom->YPos - offsetHeight);
                    }
                }
            }
        }
        fbd->reSize();
    }
    return flag;
}

bool CommonManage::addTextComponent(const QString &fileKey, int networkNumber, QJsonObject param)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            //找到对应网络
            QSharedPointer<Network> nw = fbd->networks->networkMap.value(networkNumber);
            int newNumber = fbd->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            QSharedPointer<Component> newComp = QSharedPointer<Component>(new Component());
            newComp->Name = "Text_" + strUUID.left(5);
            newComp->InstanceName = strUUID;
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = "";
            newComp->TaskOrderNumber = 0;
            newComp->Type = "Text";
            newComp->ChildType = "";
            newComp->AuxContent = param["AuxContent"].toString();
            newComp->DataType_Local = "";

            newComp->SupportsInPinType = "";
            newComp->SupportsOutPinType = "";
            newComp->SupportsInPinAdd = false;
            newComp->SupportsOutPinAdd = false;
            newComp->XPos = param["XPos"].toInt();
            newComp->YPos = param["YPos"].toInt();
            newComp->Width = param["Width"].toInt();
            newComp->Height = param["Height"].toInt();
            newComp->MinWidth = 4;


            newComp->InputPinNum = 0;
            newComp->OutputPinNum = 0;

            //加入
            fbd->components->componentMap.insert(newComp->Number, newComp);

            //添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            flag = true;
            QJsonArray ary;
            QJsonObject obj = newComp->toJsonObject();
            obj["action"] = "add";

            ary.append(obj);
            emit componentChanged(fileKey, newComp->NetworkNumber, newComp->Number, ary);
        }
    }
    return flag;
}

bool CommonManage::modifyComponentTaskAndTaskOrderNumber(const QString &fileKey, QJsonArray dataSource)
{
    bool flag = false;
    QMap<QString, QStringList> resultMap;

    foreach (const QJsonValue &value, dataSource)
    {
        QJsonObject obj = value.toObject();
        QString title = obj["title"].toString();
        QJsonArray childrens = obj["childrens"].toArray();
        QStringList childrensList;
        foreach (const QJsonValue &child, childrens)
        {
            childrensList.append(child.toString());
        }
        resultMap.insert(title, childrensList);
    }

    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        for (auto it = resultMap.begin(); it != resultMap.end(); ++it)
        {
            QString key = it.key();
            QStringList values = it.value();
            int index = 1;
            for (auto listIt = values.begin(); listIt != values.end(); ++listIt)
            {
                QString value = *listIt;
                for (QSharedPointer<Component> &com : fbd->components->componentMap)
                {
                    if (value == com->Name)
                    {
                        QJsonArray ary;
                        if (com->TaskOrderNumber != index)
                        {
                            com->TaskOrderNumber = index;
                            QJsonObject obj1;
                            obj1["action"] = "modify";
                            obj1["name"] = "TaskOrderNumber";
                            obj1["value"] = com->TaskOrderNumber;
                            ary.append(obj1);

                        }
                        if (com->TaskName != key.split(" ")[0])
                        {
                            com->TaskName = key.split(" ")[0];
                            QJsonObject obj;
                            obj["action"] = "modify";
                            obj["name"] = "TaskName";
                            obj["value"] = com->TaskName;
                            ary.append(obj);
                        }

                        if (ary.size() > 0)
                        {
                            emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);
                        }

                        break;
                    }
                }
                index++;
            }
        }
    }
    return flag;
}

bool CommonManage::modifyComponentTaskNameAndTaskOrderNumber(const QString &fileKey, int compNumber, const QString &taskName,
                                                             int taskOrderNumber)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);
            QJsonArray ary;
            if (com->TaskName != taskName)
            {
                com->TaskName = taskName;
                QJsonObject obj;
                obj["action"] = "modify";
                obj["name"] = "TaskName";
                obj["value"] = com->TaskName;
                ary.append(obj);
            }
            if (com->TaskOrderNumber != taskOrderNumber)
            {
                com->TaskOrderNumber = taskOrderNumber;
                QJsonObject obj1;
                obj1["action"] = "modify";
                obj1["name"] = "TaskOrderNumber";
                obj1["value"] = com->TaskOrderNumber;
                ary.append(obj1);

                //遍历所有块，比修改执行顺序高的全部调整一遍
                for (auto &cp : fbd->components->componentMap)
                {
                    if (cp->TaskOrderNumber >= taskOrderNumber && cp->Number != compNumber)
                    {
                        cp->TaskOrderNumber += 1;

                        QJsonArray cary;

                        QJsonObject cobj1;
                        cobj1["action"] = "modify";
                        cobj1["name"] = "TaskOrderNumber";
                        cobj1["value"] = cp->TaskOrderNumber;
                        cary.append(cobj1);

                        emit componentChanged(fileKey, cp->NetworkNumber, cp->Number, cary);
                    }
                }
            }
            if (ary.size() > 0)
            {
                emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);
            }

        }
    }


    return flag;
}

bool CommonManage::modifyComponentAuxContent(const QString &fileKey, int compNumber, const QString &newAuxContent)
{
    //qDebug() << "modifyComponentAuxContent" << fileKey << compNumber << newAuxContent;
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com_ptr = fbd->components->componentMap.value(compNumber);
            com_ptr->AuxContent = newAuxContent;
            flag = true;
            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "modify";
            obj["name"] = "AuxContent";
            obj["value"] = com_ptr->AuxContent;

            ary.append(obj);
            emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, ary);
        }
    }
    return flag;
}

bool CommonManage::modifyComponentAuxContent1(const QString &fileKey, int compNumber, const QString &newAuxContent1)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com_ptr = fbd->components->componentMap.value(compNumber);
            if (com_ptr->AuxContent1 != newAuxContent1)
            {
                com_ptr->AuxContent1 = newAuxContent1;
                flag = true;
                QJsonArray ary;
                QJsonObject obj;
                obj["action"] = "modify";
                obj["name"] = "AuxContent1";
                obj["value"] = com_ptr->AuxContent1;

                ary.append(obj);
                emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, ary);
            }
        }
    }
    return flag;
}

bool CommonManage::addVariableComponent(const QString &fileKey, int networkNumber, QJsonObject param)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            //找到对应网络
            QSharedPointer<Network> nw = fbd->networks->networkMap.value(networkNumber);
            int newNumber = fbd->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            QSharedPointer<Component> newComp = QSharedPointer<Component>(new Component());
            newComp->Name = "Variable_" + strUUID.left(5);
            newComp->InstanceName =  param["DataType"].toString() + "_" + strUUID;
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = "";
            newComp->TaskOrderNumber = 0;
            newComp->Type = "Variable";
            newComp->ChildType = param["ChildType"].toString();
            newComp->AuxContent = param["AuxContent"].toString();
            newComp->AuxContent1 = param["AuxContent1"].toString();
            newComp->DataType_Local = "";

            newComp->SupportsInPinType = "";
            newComp->SupportsOutPinType = "";
            newComp->SupportsInPinAdd = false;
            newComp->SupportsOutPinAdd = false;
            newComp->XPos = param["XPos"].toInt();
            newComp->YPos = param["YPos"].toInt();
            newComp->Height = 1;
            newComp->Width = param["Width"].toInt();

            //根据子类型确定引脚
            //Global M Local InOut Address双引脚
            //Constant I Input Expression输出引脚
            //Output Q输入引脚
            QString ctype = newComp->ChildType;
            if (ctype == "Global" || ctype == "M" || ctype == "Local" || ctype == "Static" || ctype == "Output" || ctype == "InOut"
                || ctype == "Address")
            {
                //双引脚
                newComp->InputPinNum = 1;
                newComp->OutputPinNum = 1;
                //输入引脚
                strUUID = getUUID();
                QSharedPointer<Connector> inputC = QSharedPointer<Connector>(new Connector());
                inputC->InstanceName = "IN_" + strUUID;
                inputC->PinId = 1;
                inputC->NewAdd = false;
                inputC->Name = "IN";
                inputC->DataType = param["DataType"].toString();
                inputC->SupportChangeDataType = false;
                inputC->Negated = false;
                inputC->Direction = "Input";
                inputC->FastSignal = false;
                inputC->isInitVar = false;
                inputC->IsLogical = false;
                inputC->Visible = false;
                inputC->InitValue = "";
                inputC->XPos = -1;
                inputC->YPos = 0;

                newComp->connectorList.append(inputC);

                //输出引脚
                strUUID = getUUID();
                QSharedPointer<Connector> outputC = QSharedPointer<Connector>(new Connector());
                outputC->InstanceName = "OUT_" + strUUID;
                outputC->PinId = -1;
                inputC->NewAdd = false;
                outputC->Name = "OUT";
                outputC->DataType = param["DataType"].toString();
                outputC->SupportChangeDataType = false;
                outputC->Negated = false;
                outputC->Direction = "Output";
                outputC->FastSignal = false;
                outputC->isInitVar = false;
                outputC->IsLogical = false;
                outputC->Visible = false;
                outputC->InitValue = "";
                outputC->XPos = newComp->Width;
                outputC->YPos = 0;

                newComp->connectorList.append(outputC);

            }
            else if (ctype == "Constant" || ctype == "I" || ctype == "Input" || ctype == "Expression")
            {
                //只有输出引脚
                newComp->InputPinNum = 0;
                newComp->OutputPinNum = 1;

                //输出引脚
                strUUID = getUUID();
                QSharedPointer<Connector> outputC = QSharedPointer<Connector>(new Connector());
                outputC->InstanceName = strUUID;
                outputC->PinId = -1;
                outputC->NewAdd = false;
                outputC->Name = "OUT";
                outputC->DataType = param["DataType"].toString();
                outputC->SupportChangeDataType = false;
                outputC->Negated = false;
                outputC->Direction = "Output";
                outputC->FastSignal = false;
                outputC->isInitVar = false;
                outputC->InitValue = "";
                outputC->IsLogical = false;
                outputC->Visible = false;
                outputC->XPos = newComp->Width;
                outputC->YPos = 0;

                newComp->connectorList.append(outputC);
            }
            else
            {
                //Q Output变量
                //只有输入引脚
                newComp->InputPinNum = 1;
                newComp->OutputPinNum = 0;

                //输入引脚
                strUUID = getUUID();
                QSharedPointer<Connector> inputC = QSharedPointer<Connector>(new Connector());
                inputC->InstanceName = strUUID;
                inputC->PinId = 1;
                inputC->NewAdd = false;
                inputC->Name = "IN";
                inputC->DataType = param["DataType"].toString();
                inputC->SupportChangeDataType = false;
                inputC->Negated = false;
                inputC->Direction = "Input";
                inputC->FastSignal = false;
                inputC->isInitVar = false;
                inputC->InitValue = "";
                inputC->IsLogical = false;
                inputC->Visible = false;
                inputC->XPos = -1;
                inputC->YPos = 0;

                newComp->connectorList.append(inputC);
            }
            //加入
            fbd->components->componentMap.insert(newComp->Number, newComp);

            //添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            flag = true;
            QJsonArray ary;
            QJsonObject obj = newComp->toJsonObject();
            obj["action"] = "add";

            ary.append(obj);
            emit componentChanged(fileKey, newComp->NetworkNumber, newComp->Number, ary);
        }
    }
    return flag;
}

int CommonManage::addNewVariableComponent(const QString &fileKey, int networkNumber, int leftOrRight, int parentNumber,
                                          int parentPinId, const QString &pinDataType, int xpos, int ypos)
{
    int reNubmer = 0;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            //找到对应网络
            QSharedPointer<Network> nw = fbd->networks->networkMap.value(networkNumber);
            int newNumber = fbd->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            QSharedPointer<Component> newComp = QSharedPointer<Component>(new Component());
            newComp->Name = "Variable_" + strUUID.left(5);
            newComp->InstanceName =  pinDataType + "_" + strUUID;
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = "";
            newComp->TaskOrderNumber = 0;
            newComp->Type = "Variable";
            newComp->ChildType = "???";//作用域
            newComp->AuxContent = "???";//变量名
            newComp->AuxContent1 = "???";//所属文件
            newComp->DataType_Local = "";

            newComp->SupportsInPinType = "";
            newComp->SupportsOutPinType = "";
            newComp->SupportsInPinAdd = false;
            newComp->SupportsOutPinAdd = false;
            newComp->XPos = leftOrRight == 0 ? xpos - 6 : xpos;
            newComp->YPos = ypos;
            newComp->Height = 1;
            newComp->Width = 6;
            newComp->Visible = true;
            newComp->LeftOrRight = leftOrRight;
            newComp->ParentNumber = parentNumber;
            newComp->ParentPinId = parentPinId;
            newComp->ParentPinDataType = pinDataType;

            //根据LeftOrRight确定引脚是输入还是输出
            newComp->InputPinNum = leftOrRight == 0 ? 0 : 1;
            newComp->OutputPinNum = leftOrRight == 2 ? 0 : 1;
            //引脚 IN 或 OUT
            strUUID = getUUID();
            QSharedPointer<Connector> pinC = QSharedPointer<Connector>(new Connector());
            pinC->InstanceName = (leftOrRight == 0 ? "OUT_" : "IN_") + strUUID;
            pinC->PinId = leftOrRight == 0 ? -1 : 1;
            pinC->NewAdd = false;
            pinC->Name = leftOrRight == 0 ? "OUT" : "IN";
            pinC->DataType = pinDataType;
            pinC->SupportChangeDataType = false;
            pinC->Negated = false;
            pinC->Direction = leftOrRight == 0 ? "Output" : "Input";
            pinC->FastSignal = false;
            pinC->isInitVar = false;
            pinC->InitValue = "";
            pinC->XPos = leftOrRight == 0 ? newComp->Width : 0;
            pinC->YPos = 0;
            pinC->Visible = false;//变量元件引脚不显示
            pinC->IsLogical = false;
            pinC->ChildNumber = -1;

            newComp->connectorList.append(pinC);

            //加入
            fbd->components->componentMap.insert(newComp->Number, newComp);

            //添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            qDebug() << "addNewVariableComponent" << newComp->Number <<  fbd->components->componentMap.size() << nw->componentMap.size();

            reNubmer = newComp->Number;
            QJsonArray ary;
            QJsonObject obj = newComp->toJsonObject();
            obj["action"] = "add";

            ary.append(obj);
            emit componentChanged(fileKey, newComp->NetworkNumber, newComp->Number, ary);
        }
    }
    return reNubmer;
}

bool CommonManage::modifyVariableComponent(const QString &fileKey, int networkNumber, int compNumber, const QString &scope,
                                           const QString &name, const QString &owned)
{
    bool flag = false;
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);
            com->ChildType = scope;//作用域
            com->AuxContent = name;//变量名
            com->AuxContent1 = owned;//所属文件

            flag = true;
            QJsonArray ary;
            QJsonObject obj1;
            obj1["action"] = "modify";
            obj1["name"] = "ChildType";
            obj1["value"] = com->ChildType;
            ary.append(obj1);

            QJsonObject obj2;
            obj2["action"] = "modify";
            obj2["name"] = "AuxContent";
            obj2["value"] = com->AuxContent;
            ary.append(obj2);

            QJsonObject obj3;
            obj3["action"] = "modify";
            obj3["name"] = "AuxContent1";
            obj3["value"] = com->AuxContent1;
            ary.append(obj3);

            emit componentChanged(fileKey, com->NetworkNumber, com->Number, ary);

            //对应链接同步更改
            QJsonArray aryLine;
            for (QSharedPointer<CommonConnection> &conn : fbd->connections->connectionList)
            {
                if (conn->SourceComponentNumber == com->Number)
                {
                    if (com->AuxContent != "???")
                    {
                        conn->VarName = com->AuxContent;
                        conn->VarOwned = com->AuxContent1;
                    }
                    else
                    {
                        conn->VarName = "VAR_" + conn->InstanceName;
                        conn->VarOwned = fbd->Name + "." + fbd->Code;
                    }

                    QJsonObject objLine;
                    objLine["action"] = "modify";
                    objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                    objLine["SourcePinId"] = conn->SourcePinId;
                    objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                    objLine["TargetPinId"] = conn->TargetPinId;
                    aryLine.append(objLine);

                    break;
                }
                if (conn->TargetComponentNumber == com->Number)
                {
                    if (com->AuxContent != "???")
                    {
                        conn->VarName = com->AuxContent;
                        conn->VarOwned = com->AuxContent1;
                    }
                    else
                    {
                        conn->VarName = "VAR_" + conn->InstanceName;
                        conn->VarOwned = fbd->Name + "." + fbd->Code;
                    }

                    QJsonObject objLine;
                    objLine["action"] = "modify";
                    objLine["SourceComponentNumber"] = conn->SourceComponentNumber;
                    objLine["SourcePinId"] = conn->SourcePinId;
                    objLine["TargetComponentNumber"] = conn->TargetComponentNumber;
                    objLine["TargetPinId"] = conn->TargetPinId;
                    aryLine.append(objLine);

                    break;
                }
            }
            emit lineChanged(fileKey, com->NetworkNumber, aryLine);
        }
    }
    return flag;
}

bool CommonManage::clearVariableComponent(const QString &fileKey, int networkNumber, int compNumber)
{
    return modifyVariableComponent(fileKey, networkNumber, compNumber, "???", "???", "???");
}

bool CommonManage::addSTCodeComponent(const QString &fileKey, int networkNumber, QJsonObject param)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            //找到对应网络
            QSharedPointer<Network> nw = fbd->networks->networkMap.value(networkNumber);
            int newNumber = fbd->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            QSharedPointer<Component> newComp = QSharedPointer<Component>(new Component());
            newComp->Name = "ST" + strUUID.left(5);
            newComp->InstanceName = param["ChildType"].toString() + "_" + strUUID;
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = fbd->TasksName == "" ? "T2" : fbd->TasksName; // 块任务默认cfc模式下生效，其它同文件任务
            newComp->TaskOrderNumber = fbd->getMaxOrderNumbber(newComp->TaskName) + 1;
            newComp->Type = "Block";
            newComp->ChildType = param["ChildType"].toString();
            newComp->AuxContent = param["AuxContent"].toString();
            newComp->DataType_Local = "";

            newComp->SupportsInPinType = "";
            newComp->SupportsOutPinType = "";
            newComp->SupportsInPinAdd = false;
            newComp->SupportsOutPinAdd = false;
            newComp->XPos = param["XPos"].toInt();
            newComp->YPos = param["YPos"].toInt();
            newComp->Height = param["Height"].toInt();
            newComp->Width = 11;
            newComp->Visible = true;
            newComp->LeftOrRight = 1;
            newComp->ParentNumber = -1;
            newComp->ParentPinId = 0;

            //根据子类型确定引脚
            //FUNC 函数
            //FB 功能块
            //ST ST代码块
            int inpinId = 1;//从EN开始
            int outpinId = -1;//从ENO开始
            QJsonArray connectors = param["connectors"].toArray();
            //先增加EN和ENO两个引脚
            //EN引脚
            strUUID = getUUID();
            QSharedPointer<Connector> enpin = QSharedPointer<Connector>(new Connector());
            enpin->InstanceName =  "EN_" + strUUID;
            enpin->PinId = inpinId;
            inpinId++;
            enpin->NewAdd = false;
            enpin->Name = "EN";
            enpin->DataType = "BOOL";
            enpin->SupportChangeDataType = false;
            enpin->Negated = false;
            enpin->Direction = "Input";
            enpin->FastSignal = false;
            enpin->isInitVar = false;
            enpin->InitValue = "";
            enpin->IsLogical = true;
            enpin->Visible = true;
            enpin->XPos =  0;
            enpin->YPos =  0;

            newComp->connectorList.append(enpin);
            //ENO引脚
            strUUID = getUUID();
            QSharedPointer<Connector> enopin = QSharedPointer<Connector>(new Connector());
            enopin->InstanceName =  "ENO_" + strUUID;
            enopin->PinId = outpinId;
            outpinId--;
            enopin->NewAdd = false;
            enopin->Name = "ENO";
            enopin->DataType = "BOOL";
            enopin->SupportChangeDataType = false;
            enopin->Negated = false;
            enopin->Direction = "Output";
            enopin->FastSignal = false;
            enopin->isInitVar = false;
            enopin->InitValue = "";
            enopin->IsLogical = true;
            enopin->Visible = true;
            enopin->XPos =  0;
            enopin->YPos =  0;

            newComp->connectorList.append(enopin);

            //进行引脚定位处理XPos YPos
            newComp->setPinPosOffset();
            //加入
            fbd->components->componentMap.insert(newComp->Number, newComp);

            //添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            flag = true;
            QJsonArray ary;
            QJsonObject obj = newComp->toJsonObject();
            obj["action"] = "add";

            ary.append(obj);
            emit componentChanged(fileKey, newComp->NetworkNumber, newComp->Number, ary);
        }
        if (fbd->Code == "FBD")
        {
            QJsonArray tnary = fbd->sortAllTaskOrderNumber();
            if (tnary.size() > 0)
            {
                for (int i = 0; i < tnary.size(); i++)
                {
                    QJsonObject obj = tnary.at(i).toObject();

                    QJsonArray newary;
                    newary.append(obj);

                    emit componentChanged(fileKey, obj["NetworkNumber"].toInt(), obj["ComponentNumber"].toInt(), newary);
                }
            }
        }
    }
    return flag;
}

bool CommonManage::addBlockComponent(const QString &fileKey, int networkNumber, QJsonObject param)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            //找到对应网络
            QSharedPointer<Network> nw = fbd->networks->networkMap.value(networkNumber);
            int newNumber = fbd->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            QSharedPointer<Component> newComp = QSharedPointer<Component>(new Component());
            newComp->Name = param["InstanceName"].toString() + strUUID.left(5);
            newComp->InstanceName = param["AuxContent"].toString() + "_" + strUUID;
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = fbd->TasksName == "" ? "T2" : fbd->TasksName; // 块任务默认cfc模式下生效，其它同文件任务
            newComp->TaskOrderNumber = fbd->getMaxOrderNumbber(newComp->TaskName) + 1;
            newComp->Type = "Block";
            newComp->ChildType = param["ChildType"].toString();
            newComp->AuxContent = param["AuxContent"].toString();
            newComp->DataType_Local = "";

            newComp->SupportsInPinType = param["SupportsInPinType"].toString();
            newComp->SupportsOutPinType = param["SupportsOutPinType"].toString();
            newComp->SupportsInPinAdd = false;
            newComp->SupportsOutPinAdd = false;
            newComp->XPos = param["XPos"].toInt();
            newComp->YPos = param["YPos"].toInt();
            newComp->Height = param["Height"].toInt();
            newComp->Width = 11;
            newComp->Visible = true;
            newComp->LeftOrRight = 1;
            newComp->ParentNumber = -1;
            newComp->ParentPinId = 0;

            //根据子类型确定引脚
            //FUNC 函数
            //FB 功能块
            int inpinId = 1;//从EN开始
            int outpinId = -1;//从ENO开始
            QJsonArray connectors = param["connectors"].toArray();
            //先增加EN和ENO两个引脚
            //EN引脚
            strUUID = getUUID();
            QSharedPointer<Connector> enpin = QSharedPointer<Connector>(new Connector());
            enpin->InstanceName =  "EN_" + strUUID;
            enpin->PinId = inpinId;
            inpinId++;
            enpin->NewAdd = false;
            enpin->Name = "EN";
            enpin->DataType = "BOOL";
            enpin->SupportChangeDataType = false;
            enpin->Negated = false;
            enpin->Direction = "Input";
            enpin->FastSignal = false;
            enpin->isInitVar = false;
            enpin->InitValue = "";
            enpin->IsLogical = true;
            enpin->Visible = true;
            enpin->XPos =  0;
            enpin->YPos =  0;

            newComp->connectorList.append(enpin);
            //ENO引脚
            strUUID = getUUID();
            QSharedPointer<Connector> enopin = QSharedPointer<Connector>(new Connector());
            enopin->InstanceName =  "ENO_" + strUUID;
            enopin->PinId = outpinId;
            outpinId--;
            enopin->NewAdd = false;
            enopin->Name = "ENO";
            enopin->DataType = "BOOL";
            enopin->SupportChangeDataType = false;
            enopin->Negated = false;
            enopin->Direction = "Output";
            enopin->FastSignal = false;
            enopin->isInitVar = false;
            enopin->InitValue = "";
            enopin->IsLogical = true;
            enopin->Visible = true;
            enopin->XPos =  0;
            enopin->YPos =  0;

            newComp->connectorList.append(enopin);

            //根据传入的引脚信息进行引脚与挂载变量和链接的添加
            for (int i = 0; i < connectors.count(); ++i)
            {
                if (connectors[i].isObject())
                {
                    QJsonObject obj = connectors[i].toObject();

                    strUUID = getUUID();
                    QSharedPointer<Connector> pin = QSharedPointer<Connector>(new Connector());
                    pin->InstanceName =  obj["Name"].toString() + "_" + strUUID;
                    if (obj["Direction"].toString() == "Input")
                    {
                        pin->PinId = inpinId;
                        inpinId++;
                    }
                    else if (obj["Direction"].toString() == "Output")
                    {
                        pin->PinId = outpinId;
                        outpinId--;
                    }
                    pin->NewAdd = false;
                    pin->Name = obj["Name"].toString();
                    pin->DataType = obj["DataType"].toString();
                    pin->SupportChangeDataType = false;
                    pin->Negated = false;
                    pin->Direction = obj["Direction"].toString();
                    pin->FastSignal = false;
                    pin->isInitVar = false;
                    pin->InitValue = "";
                    pin->IsLogical = false;
                    pin->Visible = true;
                    pin->XPos =  0;
                    pin->YPos =  0;

                    newComp->connectorList.append(pin);

                }
            }
            //进行引脚定位处理XPos YPos
            newComp->setPinPosOffset();
            //加入
            fbd->components->componentMap.insert(newComp->Number, newComp);

            //添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            flag = true;
            QJsonArray ary;
            QJsonObject obj = newComp->toJsonObject();
            obj["action"] = "add";

            ary.append(obj);
            emit componentChanged(fileKey, newComp->NetworkNumber, newComp->Number, ary);

            //对应引脚 添加 绑定的变量元件 和 链接
            for (QSharedPointer<Connector> &pin : newComp->connectorList)
            {
                if (!pin->IsLogical)
                {
                    //非逻辑控制引脚
                    int leftOrRight = pin->Direction == "Input" ? 0 : 2;
                    int xpos = newComp->XPos + pin->XPos;
                    int ypos = newComp->YPos + pin->YPos;
                    int newVarCompNumber = addNewVariableComponent(fileKey, networkNumber, leftOrRight, newComp->Number, pin->PinId, pin->DataType,
                                                                   xpos, ypos);
                    if (newVarCompNumber > 0)
                    {
                        //建立链接
                        if (addConnection(fileKey, newComp->Number, pin->PinId, newVarCompNumber, (pin->Direction == "Input" ? -1 : 1), false))
                        {
                            pin->ChildNumber = newVarCompNumber;
                        }
                    }
                }
            }
        }
        if (fbd->Code == "FBD")
        {
            QJsonArray tnary = fbd->sortAllTaskOrderNumber();
            if (tnary.size() > 0)
            {
                for (int i = 0; i < tnary.size(); i++)
                {
                    QJsonObject obj = tnary.at(i).toObject();

                    QJsonArray newary;
                    newary.append(obj);

                    emit componentChanged(fileKey, obj["NetworkNumber"].toInt(), obj["ComponentNumber"].toInt(), newary);
                }
            }
        }
    }
    return flag;
}

bool CommonManage::addBindVariableComponent(const QString &fileKey, int compNumber, int pinId)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com = fbd->components->componentMap.value(compNumber);
            for (auto &pin : com->connectorList)
            {
                if (!pin->IsLogical && pin->ChildNumber == -1)
                {
                    // 继续判断该引脚上是不是已经有连接了，如果有则不能绑定变量
                    bool findconn = false;
                    for (auto &conn : fbd->connections->connectionList)
                    {
                        if ((conn->SourceComponentNumber == com->Number && conn->SourcePinId == pin->PinId) ||
                            (conn->TargetComponentNumber == com->Number && conn->TargetPinId == pin->PinId))
                        {
                            findconn = true;
                            break;
                        }
                    }

                    if (!findconn)
                    {
                        // 非逻辑控制引脚
                        int leftOrRight = pin->Direction == "Input" ? 0 : 2;
                        int xpos = com->XPos + pin->XPos;
                        int ypos = com->YPos + pin->YPos;
                        int newVarCompNumber =
                            addNewVariableComponent(fileKey, com->NetworkNumber, leftOrRight, com->Number, pin->PinId,
                                                    pin->DataType, xpos, ypos);
                        if (newVarCompNumber > 0)
                        {
                            // 建立链接
                            if (addConnection(fileKey, com->Number, pin->PinId, newVarCompNumber,
                                              (pin->Direction == "Input" ? -1 : 1), false))
                            {
                                pin->ChildNumber = newVarCompNumber;
                            }
                        }

                        flag = true;
                    }
                }
            }
        }
    }
    return flag;
}

bool CommonManage::addExtendBlockComponent(const QString &fileKey, int networkNumber, QJsonObject param)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->networks->networkMap.contains(networkNumber))
        {
            //找到对应网络
            QSharedPointer<Network> nw = fbd->networks->networkMap.value(networkNumber);
            int newNumber = fbd->getMaxComonpentNumber() + 1;

            QString strUUID = getUUID();

            QSharedPointer<Component> newComp = QSharedPointer<Component>(new Component());
            newComp->Name = param["InstanceName"].toString() + "_" + strUUID.left(5);
            newComp->AuxContent = param["ChildType"].toString().replace("[Advance]", "");
            newComp->InstanceName = newComp->AuxContent + "_" + strUUID;
            newComp->ChildType = "FUNC";
            newComp->NetworkNumber = nw->Number;
            newComp->Enable = true;
            newComp->Number = newNumber;
            newComp->TaskName = fbd->TasksName == "" ? "T2" : fbd->TasksName; // 块任务默认cfc模式下生效，其它同文件任务
            newComp->TaskOrderNumber = fbd->getMaxOrderNumbber(newComp->TaskName) + 1;
            newComp->Type = "Block";

            newComp->XPos = param["XPos"].toInt();
            newComp->YPos = param["YPos"].toInt();
            newComp->Height = param["Height"].toInt();
            newComp->Width = 11;
            newComp->Visible = true;
            newComp->LeftOrRight = 1;
            newComp->ParentNumber = -1;
            newComp->ParentPinId = 0;

            QJsonArray connectors;
            //QJsonArray connectors = param["connectors"].toArray();

            //取扩展文件
            QString exfbcPath = appDir + "/Settings/extend_fbc.xml";
            ExtendFBC fbc;
            fbc.fromXml(exfbcPath);

            for (int i = 0; i < fbc.componentList->componentList.size(); i++)
            {
                QSharedPointer<ExComponent> com = fbc.componentList->componentList[i];
                if (com->ChildType == newComp->AuxContent)
                {
                    //找到定义
                    //newComp->Source = com->code->evalCode;
                    //newComp->ChildType = com->Type;
                    //引脚数据类型
                    if (newComp->AuxContent == "TYPECAST_FUNC")
                    {
                        newComp->SupportsInPinAdd = false;
                        newComp->SupportsOutPinAdd = false;
                        //提取输入类型
                        QStringList inList;
                        QStringList outList;
                        QStringList dt = com->dataType.replace("[{'", "").replace("'}]", "").split("','");
                        for (const QString &da : dt)
                        {
                            QStringList datype = da.split("2");
                            if (datype.size() > 0)
                            {
                                inList << datype[0];
                                outList << datype[1];
                            }
                        }
                        newComp->SupportsInPinType = inList.join(",");
                        newComp->SupportsOutPinType = outList.join(",");

                        //qDebug() << "inList:" << inList;
                        //qDebug() << "outList:" << outList;
                        //引脚信息
                        QJsonObject inobj;
                        inobj["Name"] = "IN";
                        inobj["Direction"] = "Input";
                        inobj["DataType"] = inList[0];
                        inobj["SupportChangeDataType"] = true;
                        inobj["DataTypeGroup"] = "local_0";
                        connectors.append(inobj);

                        QJsonObject outobj;
                        outobj["Name"] = "OUT";
                        outobj["Direction"] = "Output";
                        outobj["SupportChangeDataType"] = true;
                        outobj["DataType"] = outList[0].replace("[", "").replace("]", "").split(",")[0];
                        outobj["DataTypeGroup"] = "local_1";
                        connectors.append(outobj);

                    }
                    else if (newComp->AuxContent.indexOf("REDUCE_FUNC") >= 0)
                    {
                        //输入引脚配置
                        newComp->SupportsInPinAdd = false;
                        int pinIndex = 1;
                        for (int k = 0; k < com->var->input->elementList.size(); k++)
                        {
                            QSharedPointer<ExElement> el = com->var->input->elementList[k];
                            QString dataType = el->dataType;
                            //引脚可变类型
                            if (el->dataType.indexOf("local") > -1)
                            {
                                newComp->SupportsInPinType = com->dataType.replace("[{'", "").replace("'}]", "").replace("','", ",");
                                dataType = newComp->SupportsInPinType.split(",")[0];
                            }
                            //可增加引脚
                            if (el->ListMode)
                            {
                                newComp->SupportsInPinAdd = true;
                                QJsonObject moreobj;
                                moreobj["Name"] = "IN" + QString::number(pinIndex);
                                moreobj["Direction"] = "Input";
                                moreobj["DataType"] = dataType;
                                moreobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                moreobj["DataTypeGroup"] = el->dataType;
                                connectors.append(moreobj);
                                pinIndex++;

                                QJsonObject more2obj;
                                more2obj["Name"] = "IN" + QString::number(pinIndex);
                                more2obj["Direction"] = "Input";
                                more2obj["DataType"] = dataType;
                                more2obj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                more2obj["DataTypeGroup"] = el->dataType;
                                connectors.append(more2obj);
                                pinIndex++;
                            }
                            else
                            {
                                QJsonObject inobj;
                                inobj["Name"] = el->Name;
                                inobj["Direction"] = "Input";
                                inobj["DataType"] = dataType;
                                inobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                inobj["DataTypeGroup"] = el->dataType;
                                connectors.append(inobj);

                                pinIndex++;
                            }
                        }
                        //输出引脚配置
                        newComp->SupportsOutPinType = "follow";
                        newComp->SupportsOutPinAdd = false;
                        pinIndex = 1;
                        for (int k = 0; k < com->var->output->elementList.size(); k++)
                        {
                            QSharedPointer<ExElement> el = com->var->output->elementList[k];
                            QString dataType = el->dataType;
                            //引脚可变类型
                            if (el->dataType.indexOf("local") > -1)
                            {
                                newComp->SupportsOutPinType = com->dataType.replace("[{'", "").replace("'}]", "").replace("','", ",");
                                dataType = newComp->SupportsOutPinType.split(",")[0];
                            }
                            //可增加引脚
                            if (el->ListMode)
                            {
                                newComp->SupportsOutPinAdd = true;
                                QJsonObject outobj;
                                outobj["Name"] = "OUT" + QString::number(pinIndex);
                                outobj["Direction"] = "Output";
                                outobj["DataType"] = dataType;
                                outobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                outobj["DataTypeGroup"] = el->dataType;
                                connectors.append(outobj);

                                pinIndex++;
                            }
                            else
                            {
                                QJsonObject outobj;
                                outobj["Name"] = el->Name;
                                outobj["Direction"] = "Output";
                                outobj["DataType"] = dataType;
                                outobj["SupportChangeDataType"] = el->dataType.indexOf("local") > -1;
                                outobj["DataTypeGroup"] = el->dataType;
                                connectors.append(outobj);
                            }
                        }
                    }
                    break;
                }

            }

            int inpinId = 1;
            int outpinId = -1;
            //先增加EN和ENO两个引脚
            //EN引脚
            strUUID = getUUID();
            QSharedPointer<Connector> enpin = QSharedPointer<Connector>(new Connector());
            enpin->InstanceName =  "EN_" + strUUID;
            enpin->PinId = inpinId;
            inpinId++;
            enpin->NewAdd = false;
            enpin->Name = "EN";
            enpin->DataType = "BOOL";
            enpin->SupportChangeDataType = false;
            enpin->Negated = false;
            enpin->Direction = "Input";
            enpin->FastSignal = false;
            enpin->isInitVar = false;
            enpin->InitValue = "";
            enpin->IsLogical = true;
            enpin->Visible = true;
            enpin->XPos =  0;
            enpin->YPos =  0;

            newComp->connectorList.append(enpin);
            //ENO引脚
            strUUID = getUUID();
            QSharedPointer<Connector> enopin = QSharedPointer<Connector>(new Connector());
            enopin->InstanceName =  "ENO_" + strUUID;
            enopin->PinId = outpinId;
            outpinId--;
            enopin->NewAdd = false;
            enopin->Name = "ENO";
            enopin->DataType = "BOOL";
            enopin->SupportChangeDataType = false;
            enopin->Negated = false;
            enopin->Direction = "Output";
            enopin->FastSignal = false;
            enopin->isInitVar = false;
            enopin->InitValue = "";
            enopin->IsLogical = true;
            enopin->Visible = true;
            enopin->XPos =  0;
            enopin->YPos =  0;

            newComp->connectorList.append(enopin);

            //qDebug() << "connectors.count()" << connectors.count();
            for (int i = 0; i < connectors.count(); ++i)
            {
                if (connectors[i].isObject())
                {
                    QJsonObject obj = connectors[i].toObject();

                    strUUID = getUUID();
                    QSharedPointer<Connector> pin = QSharedPointer<Connector>(new Connector());
                    pin->InstanceName =  obj["Name"].toString() + "_" + strUUID;
                    if (obj["Direction"].toString() == "Input")
                    {
                        pin->PinId = inpinId;
                        inpinId++;
                    }
                    else if (obj["Direction"].toString() == "Output")
                    {
                        pin->PinId = outpinId;
                        outpinId--;
                    }
                    pin->NewAdd = false;
                    pin->Name = obj["Name"].toString();
                    pin->DataType = obj["DataType"].toString();
                    pin->SupportChangeDataType = obj["SupportChangeDataType"].toBool();
                    pin->DataTypeGroup = obj["DataTypeGroup"].toString();
                    pin->Negated = false;
                    pin->Direction = obj["Direction"].toString();
                    pin->FastSignal = false;
                    pin->isInitVar = false;
                    pin->InitValue = "";
                    pin->XPos =  0;
                    pin->YPos =  0;
                    pin->IsLogical = false;
                    pin->Visible = true;

                    newComp->connectorList.append(pin);

                }
            }
            //进行引脚定位处理XPos YPos
            newComp->setPinPosOffset();
            //更新所选引脚的数据类型到块上
            newComp->updateDataTypeLocal();
            //加入
            fbd->components->componentMap.insert(newComp->Number, newComp);

            //添加对应关系
            newComp->network = nw;
            nw->componentMap.insert(newComp->Number, newComp);

            flag = true;
            QJsonArray ary;
            QJsonObject obj = newComp->toJsonObject();
            obj["action"] = "add";

            ary.append(obj);
            emit componentChanged(fileKey, newComp->NetworkNumber, newComp->Number, ary);

            //对应引脚 添加 绑定的变量元件 和 链接
            for (QSharedPointer<Connector> &pin : newComp->connectorList)
            {
                if (!pin->IsLogical)
                {
                    //非逻辑控制引脚
                    int leftOrRight = pin->Direction == "Input" ? 0 : 2;
                    int xpos = newComp->XPos + pin->XPos;
                    int ypos = newComp->YPos + pin->YPos;
                    int newVarCompNumber = addNewVariableComponent(fileKey, networkNumber, leftOrRight, newComp->Number, pin->PinId, pin->DataType,
                                                                   xpos, ypos);
                    if (newVarCompNumber > 0)
                    {
                        //建立链接
                        if (addConnection(fileKey, newComp->Number, pin->PinId, newVarCompNumber, (pin->Direction == "Input" ? -1 : 1), false))
                        {
                            pin->ChildNumber = newVarCompNumber;
                        }
                    }
                }
            }
        }
        if (fbd->Code == "FBD")
        {
            fbd->sortAllTaskOrderNumber();
        }
    }
    return flag;
}

bool CommonManage::modifyConnectorNegated(const QString &fileKey, int compNumber, int pinId)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com_ptr = fbd->components->componentMap.value(compNumber);
            //找到引脚
            for (QSharedPointer<Connector> &conn_ptr : com_ptr->connectorList)
            {
                if (conn_ptr->PinId == pinId && conn_ptr->DataType == "BOOL")
                {
                    flag = true;
                    //取反操作
                    conn_ptr->Negated = !conn_ptr->Negated;
                    //发送信号
                    QJsonArray ary;
                    QJsonObject obj;
                    obj["action"] = "modify";
                    obj["name"] = "Negated";
                    obj["value"] = conn_ptr->Negated;
                    ary.append(obj);
                    emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, conn_ptr->PinId, ary);
                    break;
                }
            }
        }
    }
    return flag;
}

bool CommonManage::modifyConnectorDataType(const QString &fileKey, int compNumber, int pinId, const QString &newDataType)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com_ptr = fbd->components->componentMap.value(compNumber);
            //找到引脚
            for (QSharedPointer<Connector> &conn_ptr : com_ptr->connectorList)
            {
                if (conn_ptr->PinId == pinId && conn_ptr->SupportChangeDataType)
                {
                    if (conn_ptr->DataType == newDataType)
                    {
                        //数据类型没有变化
                        return false;
                    }
                    else
                    {
                        //判断引脚数据类型
                        if (com_ptr->SupportsInPinType.indexOf("[") > -1 || com_ptr->SupportsOutPinType.indexOf("[") > -1)
                        {
                            //有对应关系的数据类型
                            //可以直接修改类型
                            conn_ptr->DataType = newDataType;

                            //清除链接 后面重新绑定链接
                            //clearConnection(fileKey, compNumber, conn_ptr->PinId);
                            //修改其绑定的变量元件类型
                            modifyConnectorDataTypeFromParent(fileKey, com_ptr->Number, conn_ptr->PinId, newDataType);

                            QJsonArray ary;
                            QJsonObject obj;
                            obj["action"] = "modify";
                            obj["name"] = "DataType";
                            obj["value"] = conn_ptr->DataType;
                            ary.append(obj);
                            emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, conn_ptr->PinId, ary);


                            //是否是触发引脚
                            if ((conn_ptr->Direction == "Input" && com_ptr->SupportsInPinType.indexOf("[") > -1) || (conn_ptr->Direction == "Output"
                                                                                                                     && com_ptr->SupportsOutPinType.indexOf("[") > -1))
                            {}
                            else
                            {
                                //联动的类型需要跟随修改
                                QStringList typeList;
                                if (conn_ptr->Direction == "Input")
                                {
                                    int index = com_ptr->SupportsInPinType.split(",").indexOf(newDataType);
                                    typeList = com_ptr->SupportsOutPinType.split("],[")[index].replace("[", "").replace("]", "").split(",");
                                }
                                else if (conn_ptr->Direction == "Output")
                                {
                                    int index = com_ptr->SupportsOutPinType.split(",").indexOf(newDataType);
                                    typeList = com_ptr->SupportsInPinType.split("],[")[index].replace("[", "").replace("]", "").split(",");
                                }
                                if (typeList.size() > 0)
                                {
                                    for (QSharedPointer<Connector> &modify_pin : com_ptr->connectorList)
                                    {
                                        if (modify_pin->SupportChangeDataType && modify_pin->Direction != conn_ptr->Direction)
                                        {
                                            //查找该类型是否在列表中
                                            if (typeList.indexOf(modify_pin->DataType) < 0)
                                            {
                                                //不在则需要默认为列表里面的第一种类型
                                                modify_pin->DataType = typeList[0];

                                                //清除链接
                                                //clearConnection(fileKey, compNumber, modify_pin->PinId);
                                                //修改其绑定的变量元件类型
                                                modifyConnectorDataTypeFromParent(fileKey, compNumber, modify_pin->PinId, modify_pin->DataType);

                                                QJsonArray ary;
                                                QJsonObject obj;
                                                obj["action"] = "modify";
                                                obj["name"] = "DataType";
                                                obj["value"] = modify_pin->DataType;
                                                ary.append(obj);
                                                emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, modify_pin->PinId, ary);
                                            }
                                        }
                                    }

                                }
                            }
                        }
                        else
                        {
                            //无对应关系的，更改时 同时将其它可变引脚一并进行更改
                            for (QSharedPointer<Connector> &modify_pin : com_ptr->connectorList)
                            {
                                if (modify_pin->SupportChangeDataType)
                                {
                                    modify_pin->DataType = newDataType;

                                    //清除链接
                                    //clearConnection(fileKey, compNumber, modify_pin->PinId);
                                    //修改其绑定的变量元件类型
                                    modifyConnectorDataTypeFromParent(fileKey, compNumber, modify_pin->PinId, newDataType);

                                    QJsonArray ary;
                                    QJsonObject obj;
                                    obj["action"] = "modify";
                                    obj["name"] = "DataType";
                                    obj["value"] = newDataType;
                                    ary.append(obj);
                                    emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, modify_pin->PinId, ary);

                                }
                            }
                        }

                        flag = true;
                        //更新选择数据类型到块上
                        com_ptr->updateDataTypeLocal();
                        //发送信号
                        break;
                    }
                }
            }
        }
    }
    return flag;
}

bool CommonManage::modifyConnectorDataTypeFromParent(const QString &fileKey, int parentCompNumber, int parentPinId,
                                                     const QString &newDataType)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        for (QSharedPointer<Component> &com_ptr : fbd->components->componentMap)
        {
            //找到元件
            if (com_ptr->ParentNumber == parentCompNumber && com_ptr->ParentPinId == parentPinId)
            {
                //找到引脚
                for (QSharedPointer<Connector> &conn_ptr : com_ptr->connectorList)
                {
                    if (conn_ptr->DataType != newDataType)
                    {
                        conn_ptr->DataType = newDataType;
                        com_ptr->ParentPinDataType = newDataType;

                        //重新绑定链接
                        //addConnection(fileKey, parentCompNumber, parentPinId, com_ptr->Number, conn_ptr->PinId);
                        //修改对应链接的数据类型
                        for (QSharedPointer<CommonConnection> &line : fbd->connections->connectionList)
                        {
                            if ((line->SourceComponentNumber == parentCompNumber && line->SourcePinId == parentPinId
                                 && line->TargetComponentNumber == com_ptr->Number && line->TargetPinId == conn_ptr->PinId) ||
                                (line->TargetComponentNumber == parentCompNumber && line->TargetPinId == parentPinId
                                 && line->SourceComponentNumber == com_ptr->Number && line->SourcePinId == conn_ptr->PinId))
                            {
                                line->SourceDataType = newDataType;
                                line->TargetDataType = newDataType;
                                line->VarName = "VAR_" + line->InstanceName ;
                                line->VarOwned = fbd->Name + "." + fbd->Code;

                                break;
                            }
                        }

                        //修改元件记录的父引脚数据类型
                        QJsonArray qary;
                        QJsonObject qobj;
                        qobj["action"] = "modify";
                        qobj["name"] = "ParentPinDataType";
                        qobj["value"] = newDataType;
                        qary.append(qobj);
                        emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, qary);


                        QJsonArray ary;
                        QJsonObject obj;
                        obj["action"] = "modify";
                        obj["name"] = "DataType";
                        obj["value"] = newDataType;
                        ary.append(obj);
                        emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, conn_ptr->PinId, ary);

                        //数据类型变化则重新绑定变量
                        com_ptr->ChildType = "???";
                        com_ptr->AuxContent = "???";
                        com_ptr->AuxContent1 = "???";

                        QJsonArray cary;
                        QJsonObject cobj1;
                        cobj1["action"] = "modify";
                        cobj1["name"] = "ChildType";
                        cobj1["value"] = com_ptr->ChildType;
                        cary.append(cobj1);

                        QJsonObject cobj2;
                        cobj2["action"] = "modify";
                        cobj2["name"] = "AuxContent";
                        cobj2["value"] = com_ptr->AuxContent;
                        cary.append(cobj2);

                        QJsonObject cobj3;
                        cobj3["action"] = "modify";
                        cobj3["name"] = "AuxContent1";
                        cobj3["value"] = com_ptr->AuxContent1;
                        cary.append(cobj3);

                        emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, cary);

                        flag = true;
                        break;
                    }
                }

                break;
            }
        }
    }

    return flag;
}

bool CommonManage::addConnectorWithNewAdd(const QString &fileKey, int compNumber)
{
    bool flag = false;

    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com_ptr = fbd->components->componentMap.value(compNumber);
            if (com_ptr->SupportsInPinAdd)
            {
                //找到最后一个输入引脚
                for (QSharedPointer<Connector> oldconn : com_ptr->connectorList)
                {
                    if (oldconn->PinId == com_ptr->InputPinNum)
                    {
                        QString strUUID = getUUID();
                        QSharedPointer<Connector> newpin = QSharedPointer<Connector>(new Connector());
                        newpin->PinId = oldconn->PinId + 1;
                        newpin->NewAdd = true;
                        newpin->Name = "IN" + QString::number(newpin->PinId - 1);
                        newpin->InstanceName =  newpin->Name + "_" + strUUID;
                        newpin->DataType = oldconn->DataType;
                        newpin->SupportChangeDataType = oldconn->SupportChangeDataType;
                        newpin->DataTypeGroup = oldconn->DataTypeGroup;
                        newpin->Negated = false;
                        newpin->Direction = "Input";
                        newpin->FastSignal = false;
                        newpin->isInitVar = false;
                        newpin->InitValue = "";
                        newpin->XPos =  oldconn->XPos;
                        newpin->YPos =  oldconn->YPos + 1;
                        newpin->Visible = true;
                        newpin->IsLogical = false;

                        com_ptr->connectorList.append(newpin);

                        com_ptr->setPinPosOffset();

                        //信号通知
                        QJsonArray ary;
                        QJsonObject obj;
                        obj["action"] = "modify";
                        obj["name"] = "Height";
                        obj["value"] = com_ptr->Height;

                        ary.append(obj);
                        emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, ary);

                        //添加绑定的变量元件
                        int newVarCompNumber = addNewVariableComponent(fileKey, com_ptr->NetworkNumber, newpin->Direction == "Input" ? 0 : 2,
                                                                       com_ptr->Number, newpin->PinId,
                                                                       newpin->DataType, com_ptr->XPos + newpin->XPos, com_ptr->YPos + newpin->YPos);
                        if (newVarCompNumber > 0)
                        {
                            //添加与变量元件之间的链接
                            if (addConnection(fileKey, com_ptr->Number, newpin->PinId, newVarCompNumber, (newpin->Direction == "Input" ? -1 : 1), false))
                            {
                                newpin->ChildNumber = newVarCompNumber;
                            }
                        }

                        QJsonArray connary;
                        QJsonObject connobj = newpin->toJsonObject();
                        connobj["action"] = "add";

                        connary.append(connobj);
                        emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, newpin->PinId, connary);

                        flag = true;
                        break;
                    }
                }
            }

            if (com_ptr->SupportsOutPinAdd)
            {
                //找到最后一个输出引脚
                for (QSharedPointer<Connector> oldconn : com_ptr->connectorList)
                {
                    if (oldconn->PinId == -1 * (com_ptr->OutputPinNum))
                    {
                        QString strUUID = getUUID();
                        QSharedPointer<Connector> newpin = QSharedPointer<Connector>(new Connector());
                        newpin->PinId = oldconn->PinId - 1;
                        newpin->NewAdd = true;
                        newpin->Name = "OUT" + QString::number(qAbs(newpin->PinId) - 1);
                        newpin->InstanceName =  newpin->Name + "_" + strUUID;
                        newpin->DataType = oldconn->DataType;
                        newpin->SupportChangeDataType = oldconn->SupportChangeDataType;
                        newpin->DataTypeGroup = oldconn->DataTypeGroup;
                        newpin->Negated = false;
                        newpin->Direction = "Output";
                        newpin->FastSignal = false;
                        newpin->isInitVar = false;
                        newpin->InitValue = "";
                        newpin->XPos =  oldconn->XPos;
                        newpin->YPos =  oldconn->YPos + 1;
                        newpin->Visible = true;
                        newpin->IsLogical = false;

                        com_ptr->connectorList.append(newpin);

                        com_ptr->setPinPosOffset();

                        //信号通知
                        QJsonArray ary;
                        QJsonObject obj;
                        obj["action"] = "modify";
                        obj["name"] = "Height";
                        obj["value"] = com_ptr->Height;

                        ary.append(obj);
                        emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, ary);

                        QJsonArray connary;
                        QJsonObject connobj = newpin->toJsonObject();
                        connobj["action"] = "add";

                        connary.append(connobj);
                        emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, newpin->PinId, connary);

                        //添加绑定的变量元件
                        int newVarCompNumber = addNewVariableComponent(fileKey, com_ptr->NetworkNumber, newpin->Direction == "Output" ? 2 : 0,
                                                                       com_ptr->Number, newpin->PinId,
                                                                       newpin->DataType, com_ptr->XPos + newpin->XPos, com_ptr->YPos + newpin->XPos);
                        if (newVarCompNumber > 0)
                        {
                            //添加与变量元件之间的链接
                            if (addConnection(fileKey, com_ptr->Number, newpin->PinId, newVarCompNumber, (newpin->Direction == "Output" ? 1 : -1), false))
                            {
                                newpin->ChildNumber = newVarCompNumber;
                            }
                        }

                        flag = true;
                        break;
                    }
                }
            }
        }
    }

    return flag;
}

bool CommonManage::deleteConnectorWithNewAdd(const QString &fileKey, int compNumber)
{
    qDebug() << "deleteConnectorWithNewAdd" << fileKey << compNumber;
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com_ptr = fbd->components->componentMap.value(compNumber);
            if (com_ptr->SupportsInPinAdd)
            {
                //找到最后一个输入引脚
                for (int i = 0; i < com_ptr->connectorList.size(); i++)
                {
                    QSharedPointer<Connector> oldconn = com_ptr->connectorList.at(i);
                    //最后一个且是新添加引脚
                    if (oldconn->PinId == com_ptr->InputPinNum && oldconn->NewAdd)
                    {
                        //删除该引脚的链接
                        clearConnection(fileKey, com_ptr->Number, oldconn->PinId);

                        int oldconnpind = oldconn->PinId;
                        //删除该引脚
                        com_ptr->connectorList.removeAt(i);
                        i--;

                        //信号通知
                        QJsonArray connary;
                        QJsonObject connobj;
                        connobj["action"] = "delete";
                        connary.append(connobj);
                        emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, oldconnpind, connary);

                        //调整元件
                        com_ptr->setPinPosOffset();
                        //信号通知
                        QJsonArray comary;
                        QJsonObject comobj;
                        comobj["action"] = "modify";
                        comobj["name"] = "Height";
                        comobj["value"] = com_ptr->Height;
                        comary.append(comobj);
                        emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, comary);

                        flag = true;
                        //删除对应的绑定变量元件
                        deleteComponentFromParent(fileKey, com_ptr->Number, oldconnpind);

                        break;
                    }
                }
            }
            if (com_ptr->SupportsOutPinAdd)
            {
                //找到最后一个输出引脚
                for (int i = 0; i < com_ptr->connectorList.size(); i++)
                {
                    QSharedPointer<Connector> oldconn = com_ptr->connectorList.at(i);
                    //最后一个且是新添加引脚
                    if (oldconn->PinId == -1 * com_ptr->OutputPinNum && oldconn->NewAdd)
                    {
                        //删除该引脚的链接
                        clearConnection(fileKey, com_ptr->Number, oldconn->PinId);

                        int oldconnpind = oldconn->PinId;
                        //删除该引脚
                        com_ptr->connectorList.removeAt(i);
                        i--;

                        //信号通知
                        QJsonArray connary;
                        QJsonObject connobj;
                        connobj["action"] = "delete";
                        connary.append(connobj);
                        emit connectorChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, oldconnpind, connary);

                        //调整元件
                        com_ptr->setPinPosOffset();
                        //信号通知
                        QJsonArray comary;
                        QJsonObject comobj;
                        comobj["action"] = "modify";
                        comobj["name"] = "Height";
                        comobj["value"] = com_ptr->Height;
                        comary.append(comobj);
                        emit componentChanged(fileKey, com_ptr->NetworkNumber, com_ptr->Number, comary);

                        flag = true;
                        //删除对应的绑定变量元件
                        deleteComponentFromParent(fileKey, com_ptr->Number, oldconnpind);
                        break;
                    }
                }
            }
        }
    }
    return flag;
}

bool CommonManage::clearConnection(const QString &fileKey, int compNumber, int pinId)
{
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->components->componentMap.contains(compNumber))
        {
            QSharedPointer<Component> com_ptr = fbd->components->componentMap.value(compNumber);
            //找到引脚
            for (QSharedPointer<Connector> &conn_ptr : com_ptr->connectorList)
            {
                if (conn_ptr->PinId == pinId)
                {
                    flag = true;

                    QJsonArray aryLine;
                    //查询链接 如果该链接是和自己的子元件对应的，则不删除
                    for (int cindex = 0 ; cindex < fbd->connections->connectionList.size(); cindex++)
                    {
                        QSharedPointer<CommonConnection> c_ptr = fbd->connections->connectionList.at(cindex);
                        if ((c_ptr->SourceComponentNumber == com_ptr->Number && c_ptr->SourcePinId == conn_ptr->PinId && conn_ptr->ChildNumber == -1)
                            || (c_ptr->TargetComponentNumber == com_ptr->Number && c_ptr->TargetPinId == conn_ptr->PinId && conn_ptr->ChildNumber == -1))
                        {
                            //记录删除的链接
                            QJsonObject objLine;
                            objLine["action"] = "delete";
                            objLine["SourceComponentNumber"] = c_ptr->SourceComponentNumber;
                            objLine["SourcePinId"] = c_ptr->SourcePinId;
                            objLine["TargetComponentNumber"] = c_ptr->TargetComponentNumber;
                            objLine["TargetPinId"] = c_ptr->TargetPinId;
                            aryLine.append(objLine);

                            fbd->connections->connectionList.removeAt(cindex);
                            cindex--;
                        }
                    }
                    if (aryLine.count() > 0)
                    {
                        emit lineChanged(fileKey, com_ptr->NetworkNumber, aryLine);
                    }
                    break;
                }
            }
        }
    }

    return flag;
}

bool CommonManage::addConnection(const QString &fileKey, QJsonObject firstConnector, QJsonObject secondConnector)
{
    //    qDebug() << "fileKey:" << fileKey;
    qDebug() << "first:" << firstConnector;
    qDebug() << "second:" << secondConnector;

    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QString f_DataType = firstConnector["DataType"].toString();
        QString s_DataType = secondConnector["DataType"].toString();
        //引脚变量类型一致
        if (f_DataType != s_DataType)
        {
            // 获取能进行连接的所有兼容类型
            QMap<QString, QStringList> typeList = CCompatibleTypeCFC::instance()->getCompatibleTypeData();
            if (typeList.contains(f_DataType))
            {
                QStringList it = typeList.find(f_DataType).value();
                if (!it.contains(s_DataType))
                {
                    qDebug() << "The pin data type is inconsistent";
                    return false;
                }
            }
            else if (typeList.contains(s_DataType))
            {
                QStringList it = typeList.find(s_DataType).value();
                if (!it.contains(f_DataType))
                {
                    qDebug() << "The pin data type is inconsistent";
                    return false;
                }
            }
            else
            {
                qDebug() << "The pin data type is inconsistent.no define";
                return false;
            }
        }
        //不能变量到变量
        if (firstConnector["ComponentType"].toString() == "Variable" && secondConnector["ComponentType"].toString() == "Variable")
        {
            qDebug() << "var no var";
            return false;
        }
        //不能自己和自己联
        if (firstConnector["ComponentNumber"].toInt() == secondConnector["ComponentNumber"].toInt())
        {
            qDebug() << "self no self";
            return false;
        }
        //输出和输入连接Input<->Output  EN<->ENO
        if ((firstConnector["Direction"].toString() == "Input" && secondConnector["Direction"].toString() == "Input")
            || (firstConnector["Direction"].toString() == "Output" && secondConnector["Direction"].toString() == "Output"))
        {
            qDebug() << "input only output";
            return false;
        }

        QString strUUID = getUUID();
        QString instanceName = strUUID;

        QString sourceName;
        QString sourceDataType;
        int sourceComponentNumber;
        int sourcePinId;

        QString targetName;
        QString targetDataType;
        int targetComponentNumber;
        int targetPinId;

        int startx, starty, endx, endy;

        if (firstConnector["Direction"].toString() == "Input")
        {
            sourceName = secondConnector["Name"].toString();
            sourceDataType = secondConnector["DataType"].toString();
            sourceComponentNumber = secondConnector["ComponentNumber"].toInt();
            sourcePinId = secondConnector["PinId"].toInt();

            targetName = firstConnector["Name"].toString();
            targetDataType = firstConnector["DataType"].toString();
            targetComponentNumber = firstConnector["ComponentNumber"].toInt();
            targetPinId = firstConnector["PinId"].toInt();

            startx = secondConnector["XPos"].toInt();
            starty = secondConnector["YPos"].toInt();
            endx = firstConnector["XPos"].toInt();
            endy = firstConnector["YPos"].toInt();
        }
        else if (secondConnector["Direction"].toString() == "Input")
        {
            sourceName = firstConnector["Name"].toString();
            sourceDataType = firstConnector["DataType"].toString();
            sourceComponentNumber = firstConnector["ComponentNumber"].toInt();
            sourcePinId = firstConnector["PinId"].toInt();

            targetName = secondConnector["Name"].toString();
            targetDataType = secondConnector["DataType"].toString();
            targetComponentNumber = secondConnector["ComponentNumber"].toInt();
            targetPinId = secondConnector["PinId"].toInt();

            startx = firstConnector["XPos"].toInt();
            starty = firstConnector["YPos"].toInt();
            endx = secondConnector["XPos"].toInt();
            endy = secondConnector["YPos"].toInt();
        }
        else
        {
            qDebug() << "connect failed";
            return false;
        }




        QJsonArray aryLine;
        //遍历链接 查找Target
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        //两个引脚之间只能一对一
        qDebug() << "sourceName targetName" << sourceName << targetName;
        for (int cindex = 0; cindex < fbd->connections->connectionList.size(); cindex++)
        {
            QSharedPointer<CommonConnection> connection = fbd->connections->connectionList.at(cindex);
            if (connection->TargetComponentNumber == targetComponentNumber && connection->TargetPinId == targetPinId)
            {
                //信号通知
                QJsonObject objLine;
                objLine["action"] = "delete";
                objLine["SourceComponentNumber"] = connection->SourceComponentNumber;
                objLine["SourcePinId"] = connection->SourcePinId;
                objLine["TargetComponentNumber"] = connection->TargetComponentNumber;
                objLine["TargetPinId"] = connection->TargetPinId;
                aryLine.append(objLine);

                //删除该链接
                fbd->connections->connectionList.removeAt(cindex);
                cindex--;

            }
        }

        //检查 源 和 目的 是否有子变量元件
        deleteComponentFromParent(fileKey, sourceComponentNumber, sourcePinId);
        deleteComponentFromParent(fileKey, targetComponentNumber, targetPinId);

        //可以建立链接
        QSharedPointer<CommonConnection> newConnection = QSharedPointer<CommonConnection>(new CommonConnection());
        //检查该原是否已经有连接了，如果有，则实例取已经存在的
        QStringList oldinstanceName = haveConnectionFromSourcePin(fileKey, sourceComponentNumber, sourcePinId);
        if (oldinstanceName.size() > 0)
        {
            newConnection->InstanceName = oldinstanceName[0];
            newConnection->VarName = oldinstanceName[1];
            newConnection->VarOwned = oldinstanceName[2];
        }
        else
        {
            newConnection->InstanceName = instanceName;
            newConnection->VarName = "VAR_" + instanceName;
            newConnection->VarOwned = fbd->Name + "." + fbd->Code;
        }
        newConnection->SourceDataType = sourceDataType;
        newConnection->SourceComponentNumber = sourceComponentNumber;
        newConnection->SourcePinId = sourcePinId;
        newConnection->SourcePinName = sourceName;

        newConnection->TargetDataType = targetDataType;
        newConnection->TargetComponentNumber = targetComponentNumber;
        newConnection->TargetPinId = targetPinId;
        newConnection->TargetPinName = targetName;



        //计算长度 如果跨网络则长度为-1
        if (firstConnector["NetworkNumber"].toInt() == secondConnector["NetworkNumber"].toInt())
        {
            int xLength = qAbs(firstConnector["XPos"].toInt() - secondConnector["XPos"].toInt());
            int yLength = qAbs(firstConnector["YPos"].toInt() - secondConnector["YPos"].toInt());
            newConnection->Length = xLength + yLength;
            newConnection->Visible = true;
        }
        else
        {
            newConnection->Length = -1;
            newConnection->Visible = false;
        }
        //计算转折点
        newConnection->PlotPath = calculatePath(startx, starty, endx, endy, newConnection->Length);

        fbd->connections->connectionList.append(newConnection);

        flag = true;
        //消息通知链接更新

        QJsonObject objLine = newConnection->toJsonObject();
        objLine["action"] = "add";
        aryLine.append(objLine);
        emit lineChanged(fileKey, firstConnector["NetworkNumber"].toInt(), aryLine);
        if (firstConnector["NetworkNumber"].toInt() != secondConnector["NetworkNumber"].toInt())
        {
            emit lineChanged(fileKey, secondConnector["NetworkNumber"].toInt(), aryLine);
        }
    }


    return flag;
}

bool CommonManage::addConnection(const QString &fileKey, int sourceCompNumber, int sourcePinId, int targetCompNumber,
                                 int targetPinId, bool visible)
{
    qDebug() << "addConnection " << fileKey << sourceCompNumber << sourcePinId << targetCompNumber << targetPinId << visible;
    bool flag = false;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        //查找源块与引脚
        QSharedPointer<Component> sourcecom = nullptr;
        QSharedPointer<Connector> sourceconn = nullptr;
        if (fbd->components->componentMap.contains(sourceCompNumber))
        {
            sourcecom = fbd->components->componentMap.value(sourceCompNumber);
            for (QSharedPointer<Connector> &conn : sourcecom->connectorList)
            {
                if (conn->PinId == sourcePinId)
                {
                    sourceconn = conn;
                    break;
                }
            }
        }
        //查找目标块与引脚
        QSharedPointer<Component> targetcom = nullptr;
        QSharedPointer<Connector> targetconn = nullptr;
        if (fbd->components->componentMap.contains(targetCompNumber))
        {
            targetcom = fbd->components->componentMap.value(targetCompNumber);
            for (QSharedPointer<Connector> &conn : targetcom->connectorList)
            {
                if (conn->PinId == targetPinId)
                {
                    targetconn = conn;
                    break;
                }
            }
        }

        if (sourcecom != nullptr && sourceconn != nullptr && targetcom != nullptr && targetconn != nullptr)
        {
            //查找是否已经具备链接
            bool findone = false;
            for (QSharedPointer<CommonConnection> &conn : fbd->connections->connectionList)
            {
                if (conn->SourceComponentNumber == sourceCompNumber && conn->SourcePinId == sourcePinId
                    && conn->TargetComponentNumber == targetCompNumber && conn->TargetPinId == targetPinId)
                {
                    findone = true;
                    break;
                }
            }
            if (!findone)
            {
                //可以添加链接
                QString strUUID = getUUID();
                QString instanceName = strUUID;
                QString sourceName;
                QString sourceDataType;
                int sourceComponentNumber = 0;
                int sourcePinID = 0;

                QString targetName;
                QString targetDataType;
                int targetComponentNumber = 0;
                int targetPinID = 0;

                int startx = 0, starty = 0, endx = 0, endy = 0;

                if (targetconn->Direction == "Input")
                {
                    sourceName = sourceconn->Name;
                    sourceDataType = sourceconn->DataType;
                    sourceComponentNumber = sourcecom->Number;
                    sourcePinID = sourceconn->PinId;

                    targetName = targetconn->Name;
                    targetDataType = targetconn->DataType;
                    targetComponentNumber = targetcom->Number;
                    targetPinID = targetconn->PinId;

                    startx = sourcecom->XPos + sourceconn->XPos;
                    starty = sourcecom->YPos + sourceconn->YPos;
                    endx = targetcom->XPos + targetconn->XPos;
                    endy =  targetcom->YPos + targetconn->YPos;
                }
                else if (sourceconn->Direction == "Input")
                {
                    sourceName = targetconn->Name;
                    sourceDataType = targetconn->DataType;
                    sourceComponentNumber = targetcom->Number;
                    sourcePinID = targetconn->PinId;

                    targetName = sourceconn->Name;
                    targetDataType = sourceconn->DataType;
                    targetComponentNumber = sourcecom->Number;
                    targetPinID = sourceconn->PinId;

                    startx = targetcom->XPos + targetconn->XPos;
                    starty = targetcom->YPos + targetconn->YPos;
                    endx = sourcecom->XPos + sourceconn->XPos;
                    endy =  sourcecom->YPos + sourceconn->YPos;
                }


                QSharedPointer<CommonConnection> newConnection = QSharedPointer<CommonConnection>(new CommonConnection());
                //检查该原是否已经有连接了，如果有，则实例取已经存在的
                QStringList oldinstanceName = haveConnectionFromSourcePin(fileKey, sourcecom->Number, sourceconn->PinId);
                if (oldinstanceName.size() > 0)
                {
                    newConnection->InstanceName = oldinstanceName[0];
                    newConnection->VarName = oldinstanceName[1];
                    newConnection->VarOwned = oldinstanceName[2];
                }
                else
                {
                    newConnection->InstanceName = instanceName;
                    newConnection->VarName = "VAR_" + instanceName;
                    newConnection->VarOwned = fbd->Name + "." + fbd->Code;
                }
                newConnection->SourceDataType = sourceDataType;
                newConnection->SourceComponentNumber = sourceComponentNumber;
                newConnection->SourcePinId = sourcePinID;
                newConnection->SourcePinName = sourceName;

                newConnection->TargetDataType = targetDataType;
                newConnection->TargetComponentNumber = targetComponentNumber;
                newConnection->TargetPinId = targetPinID;
                newConnection->TargetPinName = targetName;



                //计算长度 如果跨网络或者源元件是开始元件则长度为-1
                if ((sourcecom->NetworkNumber == targetcom->NetworkNumber) && sourcecom->AuxContent1 != "Start")
                {
                    int xLength = qAbs(startx - endx);
                    int yLength = qAbs(starty - endy);
                    newConnection->Length = xLength + yLength;
                    newConnection->Visible = visible;
                }
                else
                {
                    newConnection->Length = -1;
                    newConnection->Visible = false;
                }
                //计算转折点
                newConnection->PlotPath = calculatePath(startx, starty, endx, endy, newConnection->Length);

                fbd->connections->connectionList.append(newConnection);

                flag = true;
                //消息通知链接更新
                QJsonArray aryLine;
                QJsonObject objLine = newConnection->toJsonObject();
                objLine["action"] = "add";

                aryLine.append(objLine);
                emit lineChanged(fileKey, sourcecom->NetworkNumber, aryLine);
                if (sourcecom->NetworkNumber != targetcom->NetworkNumber)
                {
                    emit lineChanged(fileKey, targetcom->NetworkNumber, aryLine);
                }
            }
            else
            {
                qDebug() << "addConnection already have one connection!";
            }
        }
        else
        {
            qDebug() << "addConnection no find source or target!";
        }

    }

    return flag;
}

QStringList CommonManage::haveConnectionFromSourcePin(const QString &fileKey, int sourceComponentNumber, int sourcePinId)
{
    QStringList list;
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        for (QSharedPointer<CommonConnection> &c_ptr : fbd->connections->connectionList)
        {
            if (c_ptr->SourceComponentNumber == sourceComponentNumber && c_ptr->SourcePinId == sourcePinId)
            {
                list.push_back(c_ptr->InstanceName);
                list.push_back(c_ptr->VarName);
                list.push_back(c_ptr->VarOwned);

                break;
            }
        }
    }
    qDebug() << "haveConnectionFromSourcePin" << list;
    return list;
}

QString CommonManage::calculatePath(int startX, int startY, int endX, int endY, int length)
{
    //qDebug() << "start---end:" << startX << startY << endX << endY << length;
    //todo 计算两点之间路径
    QString path = "[]";
    if (length < 0)
    {
        path = "[{\"x\":" + QString::number(startX) + ",\"y\":" + QString::number(startY) + "},{\"x\":" +
               QString::number(endX) + ",\"y\":" + QString::number(endY) + "}]";
    }
    else if (length > 200)
    {
        path = "[{\"x\":" + QString::number(startX) + ",\"y\":" + QString::number(startY) + "},{\"x\":" +
               QString::number(startX) + ",\"y\":" + QString::number(startY) + "}]";
    }
    else
    {
        path = "[{\"x\":" + QString::number(startX) + ",\"y\":" + QString::number(startY) + "},{\"x\":" +
               QString::number(endX) + ",\"y\":" + QString::number(endY) + "}]";
        //判断startX endX的大小
        if (startX <= endX)
        {
            int xspan =  qFloor((endX - startX) / 2);
            if (xspan > 0)
            {
                path = "[{\"x\":" + QString::number(startX) + ",\"y\":" + QString::number(startY) + "}," +
                       "{\"x\":" + QString::number(startX + xspan) + ",\"y\":" + QString::number(startY) + "}," +
                       "{\"x\":" + QString::number(startX + xspan) + ",\"y\":" + QString::number(endY) + "}," +
                       "{\"x\":" + QString::number(endX) + ",\"y\":" + QString::number(endY) + "}]";
            }
        }
        else
        {
            int yspan =  qFloor((endY - startY) / 2);

            path = "[{\"x\":" + QString::number(startX) + ",\"y\":" + QString::number(startY) + "}," +
                   "{\"x\":" + QString::number(startX) + ",\"y\":" + QString::number(startY + yspan) + "}," +
                   "{\"x\":" + QString::number(endX) + ",\"y\":" + QString::number(startY + yspan) + "}," +
                   "{\"x\":" + QString::number(endX) + ",\"y\":" + QString::number(endY) + "}]";
        }

    }
    //qDebug() << "path:" << path;
    return path;
}

bool CommonManage::checkBlockOutConnection(const QString &fileKey)
{
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        return fbd->checkBlockOutConnection();
    }
    return false;
}

bool CommonManage::checkCycle(const QString &fileKey)
{
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();

        return fbd->checkCycle();
    }
    return false;
}

QString CommonManage::checkValidity(CommonFile *fbd)
{
    qDebug() << "checkValidity" << fbd->filePath;
    QString error;
    if (fbd != nullptr)
    {
        //1.检查元件数量大于1的时候至少有一个start元件
        if (fbd->components->componentMap.size() > 0)
        {
            //            bool findone = false;
            //            for (auto &com  : fbd->components->componentMap)
            //            {
            //                if (com->AuxContent1 == "Start")
            //                {
            //                    findone = true;
            //                    break;
            //                }
            //            }
            //            if (!findone)
            //            {
            //                error += "File " + fbd->Name + "." + fbd->Code + " is no start component!\n";
            //            }
        }
        else
        {
            //空文件
            error += "File " + fbd->Name + "." + fbd->Code + " is empty!\n";
        }
        //2.元件与网络对应
        QMap<int, QString> networknumber;
        for (auto &nw : fbd->networks->networkMap)
        {
            networknumber.insert(nw->Number, nw->Label);
        }
        for (auto &com  : fbd->components->componentMap)
        {
            if (!networknumber.contains(com->NetworkNumber))
            {
                error += "File " + fbd->Name + "." + fbd->Code + "," + com->Name + " component wrong network number!\n";
            }
        }
        //3.没有悬空的引脚 变量元件已设置，所有的引脚（排除EN ENO引脚）在链接中都有
        for (auto &com : fbd->components->componentMap)
        {
            if (com->Type == "Block")
            {
                for (auto &con : com->connectorList)
                {
                    if (!con->IsLogical)
                    {
                        bool findone = false;
                        for (auto &conn : fbd->connections->connectionList)
                        {
                            if ((con->Direction == "Input" && conn->TargetPinId == con->PinId && conn->TargetComponentNumber == com->Number)
                                || (con->Direction == "Output" && conn->SourcePinId == con->PinId && conn->SourceComponentNumber == com->Number))
                            {
                                findone = true;
                                break;
                            }
                        }
                        if (!findone)
                        {
                            error += "File " + fbd->Name + "." + fbd->Code + "," + com->Name + " component " + con->Name + "connector no connection!\n";
                        }
                    }
                }
            }
            else if (com->Type == "Variable")
            {
                if (com->AuxContent == "???" || com->AuxContent == "")
                {
                    error += "File " + fbd->Name + "." + fbd->Code + " have empty variable component!\n";
                }
            }
        }
        //4.链接中的源与目的都存在
        for (auto &conn : fbd->connections->connectionList)
        {
            bool findsource = false;
            bool findtarget = false;
            for (auto &com : fbd->components->componentMap)
            {
                if (com->Number == conn->SourceComponentNumber)
                {
                    //找引脚
                    for (auto &con : com->connectorList)
                    {
                        if (con->PinId == conn->SourcePinId)
                        {
                            findsource = true;
                            break;
                        }
                    }
                }
                else if (com->Number == conn->TargetComponentNumber)
                {
                    //找引脚
                    for (auto &con : com->connectorList)
                    {
                        if (con->PinId == conn->TargetPinId)
                        {
                            findtarget = true;
                            break;
                        }
                    }
                }
                if (findsource && findtarget)
                {
                    break;
                }
            }
            if (!findsource || !findtarget)
            {
                error += "File " + fbd->Name + "." + fbd->Code + " have invalid link!\n";
            }
        }


    }
    return error;
}

QJsonArray CommonManage::getOnlineValues(const QString &fileKey)
{
    QJsonArray ary;

    return ary;
}

bool CommonManage::CreateFile(const QString &fileName, const QString &fileDir)
{
    QDir dir(fileDir);
    QString filePath = dir.absoluteFilePath(fileName);

    CommonFile newfile;
    newfile.Name = fileName;
    newfile.Code = fileName.right(3);
    newfile.toXml(filePath);
    qDebug() << "生成FBD文件完成";
    return true;
}

bool CommonManage::ReadFile(const QString &fileName, const QString &fileDir)
{
    QDir dir(fileDir);
    QString filePath = dir.absoluteFilePath(fileName);
    if (QFile::exists(filePath))
    {
        CommonFile readfile;
        readfile.fromXml(filePath);
        qDebug() << "读取生成FBD文件完成";
        return true;
    }
    else
    {
        return false;
    }
}

void CommonManage::putVarComponentInOrder(const QString &fileKey)
{
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        qDebug() << "putVarComponentInOrder connectionList size:" << fbd->connections->connectionList.size();
        //遍历所有连接，查找长度超过0的
        for (QSharedPointer<CommonConnection> conn : fbd->connections->connectionList)
        {
            if (conn->Length != 0)
            {
                //源元件
                QSharedPointer<Component> sourceCom = fbd->searchComponentFromNumber(conn->SourceComponentNumber);
                //目标元件
                QSharedPointer<Component> targetCom = fbd->searchComponentFromNumber(conn->TargetComponentNumber);

                //qDebug() << "putVarComponentInOrder" << sourceCom->toJsonObject() << targetCom->toJsonObject();
                //先处理块右边的，然后处理左边的
                if (targetCom != nullptr && targetCom->Type == "Variable" && targetCom->AuxContent1 != "Start")
                {
                    //块的右边 源元件的引脚
                    QSharedPointer<Connector> scon_ptr = sourceCom->searchConnectorFromPinId(conn->SourcePinId);
                    if (scon_ptr != nullptr)
                    {
                        int x = sourceCom->XPos + sourceCom->Width;
                        int y = sourceCom->YPos + scon_ptr->YPos;
                        qDebug() << "putVarComponentInOrder right side move:" << fileKey << targetCom->Number << x << y;

                        targetCom->XPos = x;
                        targetCom->YPos = y;
                        targetCom->LeftOrRight = 0;
                        conn->Length = 0;
                        conn->PlotPath = calculatePath(x, y, x, y, 0);

                    }
                }
                else if (sourceCom != nullptr && sourceCom->Type == "Variable" && sourceCom->AuxContent1 != "Start")
                {
                    //块的左边 目标元件的引脚
                    QSharedPointer<Connector> tcon_ptr = targetCom->searchConnectorFromPinId(conn->TargetPinId);
                    if (tcon_ptr != nullptr)
                    {
                        int x = targetCom->XPos - sourceCom->Width;
                        int y = targetCom->YPos + tcon_ptr->YPos;
                        qDebug() << "putVarComponentInOrder left side move:" << fileKey << sourceCom->Number << x << y;
                        sourceCom->XPos = x;
                        sourceCom->YPos = y;
                        sourceCom->LeftOrRight = 2;
                        conn->Length = 0;
                        conn->PlotPath = calculatePath(x, y, x, y, 0);
                    }
                }
                else
                {
                    qDebug() << "putVarComponentInOrder ???" << sourceCom->toJsonObject() << targetCom->toJsonObject();
                }

            }
        }
        qDebug() << "putVarComponentInOrder end";

    }

}

void CommonManage::putStartOnEN(const QString &fileKey)
{
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        if (fbd->networks->networkMap.size() > 0)
        {
            QSharedPointer<Network> nw = fbd->networks->networkMap.first();

            if (fbd->components->componentMap.size() > 0)
            {
                if (fbd->components->componentMap.size() == 1 && fbd->components->componentMap.first()->AuxContent1 == "Start")
                {
                    //如果只有一个元件，且该元件是开始元件，则删除该元件
                    deleteComponent(fileKey, fbd->components->componentMap.first()->Number);
                }
                else
                {
                    //寻找是否已经存在start变量元件，且childtype 为Constant
                    bool findone = false;

                    QSharedPointer<Component> startcom = nullptr;
                    for (QSharedPointer<Component> &com : fbd->components->componentMap)
                    {
                        if (com->ChildType == "Constant" && com->AuxContent1 == "Start")
                        {
                            startcom = com;
                            findone = true;
                            break;
                        }
                    }
                    if (!findone)
                    {
                        int newNumber = fbd->getMaxComonpentNumber() + 1;
                        QString strUUID = getUUID();
                        startcom = QSharedPointer<Component>(new Component());
                        startcom->Name = "Start" + strUUID.left(5);
                        startcom->InstanceName =  "Start_" + strUUID;
                        startcom->NetworkNumber = nw->Number;
                        startcom->Enable = true;
                        startcom->Number = newNumber;
                        startcom->TaskName = "";
                        startcom->TaskOrderNumber = 0;
                        startcom->Type = "Variable";
                        startcom->ChildType = "Constant";//作用域
                        startcom->AuxContent = "1";//变量名
                        startcom->AuxContent1 = "Start";//所属文件
                        startcom->DataType_Local = "";

                        startcom->SupportsInPinType = "";
                        startcom->SupportsOutPinType = "";
                        startcom->SupportsInPinAdd = false;
                        startcom->SupportsOutPinAdd = false;
                        startcom->XPos = 0;
                        startcom->YPos = 0;
                        startcom->Height = 0;
                        startcom->Width = 0;
                        startcom->Visible = false;
                        startcom->LeftOrRight = 0;
                        startcom->ParentNumber = -1;
                        startcom->ParentPinId = 0;
                        startcom->ParentPinDataType = "";

                        //只有输出
                        startcom->InputPinNum = 0;
                        startcom->OutputPinNum = 1;
                        //引脚OUT
                        strUUID = getUUID();
                        QSharedPointer<Connector> pinC = QSharedPointer<Connector>(new Connector());
                        pinC->InstanceName = "OUT_" + strUUID;
                        pinC->PinId = -1;
                        pinC->NewAdd = false;
                        pinC->Name = "OUT";
                        pinC->DataType = "BOOL";
                        pinC->SupportChangeDataType = false;
                        pinC->Negated = false;
                        pinC->Direction = "Output";
                        pinC->FastSignal = false;
                        pinC->isInitVar = false;
                        pinC->InitValue = "";
                        pinC->XPos = 0;
                        pinC->YPos = 0;
                        pinC->Visible = false;//变量元件引脚不显示
                        pinC->IsLogical = true;
                        pinC->ChildNumber = -1;

                        startcom->connectorList.append(pinC);

                        //加入
                        fbd->components->componentMap.insert(startcom->Number, startcom);

                        //添加对应关系
                        startcom->network = nw;
                        nw->componentMap.insert(startcom->Number, startcom);

                        QJsonArray ary;
                        QJsonObject obj = startcom->toJsonObject();
                        obj["action"] = "add";

                        ary.append(obj);
                        emit componentChanged(fileKey, startcom->NetworkNumber, startcom->Number, ary);
                    }

                    if (startcom != nullptr)
                    {
                        //查找所有空置的EN引脚 和 Start引脚建立链接
                        for (QSharedPointer<Component> &com : fbd->components->componentMap)
                        {
                            if (com->Type == "Block")
                            {
                                bool findone = false;
                                for (QSharedPointer<CommonConnection> &conn : fbd->connections->connectionList)
                                {
                                    if (conn->TargetPinId == 1 && conn->TargetComponentNumber == com->Number)
                                    {
                                        findone = true;
                                        break;
                                    }
                                }
                                if (!findone)
                                {
                                    //建立链接
                                    qDebug() << "putStartOnEN addConnection" << startcom->Number << -1 << com->Number << 1;
                                    addConnection(fileKey, startcom->Number, -1, com->Number, 1, false);
                                }
                            }
                        }
                    }
                    else
                    {
                        qDebug() << "putStartOnEN startcom is nullptr";
                    }
                }
            }
        }
    }
}

void CommonManage::cleanFailedVariableComponent(const QString &fileKey)
{
    if (CommonFileList.contains(fileKey))
    {
        QSharedPointer<CommonFile> cFile = CommonFileList[fileKey];
        CommonFile *fbd = cFile.data();
        QList<int> toDeleteNetworkNumbers;
        QList<int> toDeleteCompNumbers;
        for (auto &com : fbd->components->componentMap)
        {
            if (com->Type == "Variable" && com->AuxContent1 != "Start")
            {
                //在链接中查找
                bool findone = false;
                for (QSharedPointer<CommonConnection> &conn : fbd->connections->connectionList)
                {
                    if (conn->SourceComponentNumber == com->Number || conn->TargetComponentNumber == com->Number)
                    {
                        findone = true;
                        break;
                    }
                }
                if (!findone)
                {
                    //没有找到则删除
                    int networknumber = com->NetworkNumber;
                    int compnumber = com->Number;
                    toDeleteNetworkNumbers.append(com->NetworkNumber);
                    toDeleteCompNumbers.append(com->Number);
                    //删除network中对应关系
                    if (fbd->networks->networkMap.contains(networknumber))
                    {
                        if (fbd->networks->networkMap.value(networknumber)->componentMap.contains(compnumber))
                        {
                            fbd->networks->networkMap.value(networknumber)->componentMap.remove(compnumber);
                        }
                    }
                }
            }
        }
        qDebug() << "deleteComponent" << toDeleteNetworkNumbers << toDeleteCompNumbers;
        for (int i = 0; i < toDeleteNetworkNumbers.size(); i++)
        {
            int networknumber = toDeleteNetworkNumbers.at(i);
            int compnumber = toDeleteCompNumbers.at(i);

            fbd->components->componentMap.remove(compnumber);

            QJsonArray ary;
            QJsonObject obj;
            obj["action"] = "delete";
            ary.append(obj);
            emit componentChanged(fileKey, networknumber, compnumber, ary);
            qDebug() << "deleteComponent ary" << networknumber << compnumber << ary;
        }
    }
}

QString CommonManage::getUUID()
{
    return QUuid::createUuid().toString().remove("{").remove("}").remove("-");
}
