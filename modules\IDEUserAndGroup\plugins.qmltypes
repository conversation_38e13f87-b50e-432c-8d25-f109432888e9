import QtQuick.tooling 1.2

// This file describes the plugin-supplied types contained in the library.
// It is used for QML tooling purposes only.
//
// This file was auto-generated by qmltyperegistrar.

Module {
    dependencies: ["QtQuick 2.0"]
    Component {
        file: "userandgroupitem.h"
        name: "UserAndGroupItem"
        prototype: "QQuickPaintedItem"
        exports: [
            "IDEUserAndGroup/UserAndGroupItem 1.0",
            "IDEUserAndGroup/UserAndGroupItem 1.1",
            "IDEUserAndGroup/UserAndGroupItem 1.11",
            "IDEUserAndGroup/UserAndGroupItem 1.4",
            "IDEUserAndGroup/UserAndGroupItem 1.7"
        ]
        exportMetaObjectRevisions: [0, 1, 11, 4, 7]
    }
}
