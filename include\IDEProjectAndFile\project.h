#ifndef _PROJECT_H_
#define _PROJECT_H_

#include <QString>
#include <QObject>
#include <QMap>
#include <QJsonArray>
#include <QJsonObject>
#include <QFile>
#include <QUuid>

class Project
{
public:
    Project();
    ~Project();

    void setValue(const QString& key, const QVariant& value);
    QVariant getValue(const QString& key) const;
    void fromJson(const QJsonObject& json);

    QString getName() const;
    QString getPath() const;
    QString getVersion() const;
    QString getAuthor() const;
    QString getDescription() const;
    QString getProtection() const;
    QString getIcon() const;
    QString getCreateCompute() const;
    QString getCreateTime() const;
    QString getLastModifyTime() const;

    QJsonObject toJson() const;

    QString getUUID() const;
    void setUUID(const QString& uuid);

private:
    QMap<QString, QVariant> properties;
    QString m_uuid;
};

#endif
