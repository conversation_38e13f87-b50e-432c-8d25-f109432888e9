﻿import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import "qrc:/qml"
import "qrc:/qml/gv"
import "qrc:/SimpleUI/qml"
import "qrc:/qml/control/common"

Rectangle {
    id: type_table

    property string deviceName
    property var dataTypeList
    property var selectIdList
    property var flatTreeData

    ColumnLayout {
        id: column_layout
        anchors.fill: parent
        spacing: 0

        RowLayout {
            id: row_layout
            Layout.fillWidth: true
            Layout.maximumHeight: 36
            Layout.leftMargin: 10
            Layout.rightMargin: 10
            spacing: 10

            QkButton {
                id: add_row
                text: "添加"
                Layout.preferredWidth: 60
                Layout.preferredHeight: 25
                onClicked: {
                    popup.raiseItem = add_data_type
                    popup.open()
                }
            }

            QkButton {
                id: delete_row
                text: "删除"
                Layout.preferredWidth: 60
                Layout.preferredHeight: 25
                onClicked: {
                    if (type_table.selectIdList.size > 0) {
                        messageDialog.show(
                            "是否删除选中项", () => {
                                for (let key of type_table.selectIdList) {
                                    const result = type_table.flatTreeData.find(item => item.parentID === key)
                                    if (result) {
                                        message_dialog.show("选择的项中存在子项,请先删除子项")
                                        return
                                    } else {
                                        const res = VariableManage.deleteDataType(key)
                                        if (!res) {
                                            message_dialog.show("删除失败")
                                            return
                                        }
                                        getDataBind()
                                    }
                                }
                            }, () => {
                            }, "confirm")

                    } else {
                        messageDialog.show("还未选择数据类型，请先选择数据")
                    }
                }
            }

            Item {
                Layout.fillWidth: true
                Layout.fillHeight: true
            }

            QkButton {
                id: reload_data
                text: "重新加载"
                Layout.preferredWidth: 80
                Layout.preferredHeight: 25
                onClicked: {
                    getDataBind()
                }
            }
        }

        O_TreeTable {
            id: tree_table
            Layout.fillWidth: true
            Layout.fillHeight: true
            depthPadding: 15
            columnSource: [
                {
                    title: "ID",
                    dataIndex: "_key",
                    width: 100,
                    readOnly: true
                },
                {
                    title: '选择',
                    dataIndex: 'isSelect',
                    width: 50,
                    minimumWidth: 50,
                    maximumWidth: 50
                },
                {
                    title: '层级',
                    dataIndex: 'depth',
                    width: 100,
                    readOnly: true
                },
                {
                    title: '名称',
                    dataIndex: 'name',
                    width: (type_table.width - 680) / 2,
                    editDelegate: name_text_field
                },
                {
                    title: '数据类型',
                    dataIndex: 'dataType',
                    width: 250,
                    editDelegate: com_combobox
                },
                {
                    title: '数组长度',
                    dataIndex: 'arrayLength',
                    width: 180,
                    minimumWidth: 180,
                    maximumWidth: 180,
                    editDelegate: array_length_spin,
                },
                {
                    title: '描述',
                    dataIndex: 'description',
                    width: (type_table.width - 680) / 2,
                    editDelegate: description_text_field
                }
            ]
        }
    }

    MainPopup {
        id: popup
        anchors.centerIn: parent
    }

    Component {
        id: add_data_type
        Rectangle {
            width: 400
            height: 170
            QkButtonRow {
                id: title
                z: 10
                QkLabel {
                    anchors.left: parent.left
                    anchors.leftMargin: 10

                    anchors.verticalCenter: parent.verticalCenter
                    text: qsTr("Add") + (trans ? trans.transString : "")
                }
            }
            ColumnLayout {
                width:parent.width
                anchors.top: title.bottom
                anchors.bottom: parent.bottom
                spacing: 10

                // 输入区域
                RowLayout {
                    Layout.fillWidth: true
                    Layout.margins: 20
                    spacing: 15

                    QkLabel {
                        text: qsTr("变量名:")
                        Layout.alignment: Qt.AlignVCenter
                        Layout.preferredWidth: 60 // 固定标签宽度
                        verticalAlignment: Text.AlignVCenter
                        horizontalAlignment: Text.AlignRight
                    }

                    QkTextField {
                        id: dataTypeName
                        Layout.fillWidth: true
                        Layout.alignment: Qt.AlignVCenter
                        text: ""
                        selectByMouse: true // 启用文本选择
                        maximumLength: 30
                    }
                }

                // 修改后的按钮区域
                RowLayout {
                    Layout.fillWidth: true
                    Layout.alignment: Qt.AlignBottom
                    Layout.bottomMargin: 20
                    spacing: 20

                    // 左侧占位弹簧
                    Item {
                        Layout.fillWidth: true
                    }

                    // 关闭按钮
                    QkButton {
                        text: qsTr("Close") + (trans ? trans.transString : "")
                        Layout.preferredWidth: 100
                        Layout.rightMargin: 50
                        onClicked: {
                            popup.close()
                        }
                    }

                    // 确认按钮
                    QkButton {
                        isGradientBgColor: true
                        text: qsTr("Confirm") + (trans ? trans.transString : "")
                        Layout.preferredWidth: 100
                        Layout.leftMargin: 50
                        onClicked: {
                            if (serviceInterface.checkDataTypeNameSyntax(type_table.deviceName, dataTypeName.text) && dataTypeName.text) {
                                if (type_table.selectIdList.size === 0) {
                                    const res = VariableManage.addMainDataType(type_table.deviceName, dataTypeName.text)
                                    if (!res) {
                                        messageDialog.show("添加失败")
                                    } else {
                                        getDataBind()
                                    }
                                } else if (type_table.selectIdList.size === 1) {
                                    for (let key of type_table.selectIdList) {
                                        const parentNode = type_table.flatTreeData.find(item => item._key === key)
                                        if (parentNode) {
                                            const validType = (parentNode.type === "USER" || parentNode.dataType === "STRUCT")
                                            if (validType) {
                                                const res = VariableManage.addChildDataType(type_table.deviceName, parentNode._key, dataTypeName.text)
                                                if (!res) {
                                                    messageDialog.show("添加失败")
                                                } else {
                                                    getDataBind()
                                                }
                                            } else {
                                                messageDialog.show("只能给用户自定义类型和结构类型添加子项")
                                            }
                                        } else {
                                            messageDialog.show("添加失败，请重新加载")
                                        }
                                    }
                                } else {
                                    messageDialog.show("只能选择一个数据类型")
                                }
                                popup.close()
                            } else {
                                messageDialog.show("变量名不合法")
                            }
                        }
                    }

                    // 右侧占位弹簧
                    Item {
                        Layout.fillWidth: true
                    }
                }
            }
        }
    }

    Component {
        id: com_checkbox
        Item {
            property var rowData: tree_table.getRow(row)

            S_CheckBox {
                anchors.centerIn: parent
                checked: type_table.selectIdList.has(rowData && rowData._key)
                Layout.alignment: Qt.AlignVCenter
                onCheckedChanged: {
                    if (checked) {
                        type_table.selectIdList.add(rowData._key)
                    } else {
                        type_table.selectIdList.delete(rowData._key)
                    }
                }
            }
        }
    }

    Component {
        id: name_text_field
        TextField {
            property var rowData: tree_table.getRow(row)
            anchors.fill: parent
            text: String(display) || ""
            font.pixelSize: 14
            visible: !rowData.children && rowData.depth > 1
            validator: RegularExpressionValidator {
                regularExpression: GlobalVariable.varname_regular
            }
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.name !== text && text) {
                        if (text.length > 2) {
                            const res = VariableManage.modifyChildDataType(
                                rowData._key,
                                rowData.dataType,
                                text,
                                rowData.shortName,
                                rowData.arrayLength,
                                rowData.description
                            )
                            if (!res) {
                                messageDialog.show("修改失败")
                            } else {
                                editTextChaged(text)
                            }
                        } else {
                            messageDialog.show("名称不得少于两个字符")
                        }
                        console.log(JSON.stringify(rowData))
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    Component {
        id: com_combobox
        ComboBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            model: type_table.dataTypeList
            editText: display
            font.pixelSize: 14
            visible: !rowData.children && rowData.depth > 1
            currentIndex: type_table.dataTypeList.findIndex(dataType => dataType === editText)

            onActiveFocusChanged: {
                if (!activeFocus)
                    tableView.closeEditor()
            }

            onActivated: {
                if (!VariableManage.checkSelfReference(rowData._key, editText)) {
                    messageDialog.show("不允许自引用和交叉引用")
                    tableView.closeEditor()
                    return
                }
                const res = VariableManage.modifyChildDataType(
                    rowData._key,
                    editText,
                    rowData.name,
                    rowData.shortName,
                    (editText === "STRUCT" ? 1 : rowData.arrayLength),
                    rowData.description
                )
                if (!res) {
                    messageDialog.show("修改失败")
                } else {
                    tree_table.setRow(row, {arrayLength: 1})
                    editTextChaged(editText)
                }
                tableView.closeEditor()
            }
        }
    }

    Component {
        id: array_length_spin
        SpinBox {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            from: 1
            to: 20000
            editable: true
            value: Number(display)
            font.pixelSize: 14
            visible: !rowData.children && rowData.depth > 1 && rowData.dataType !== "STRUCT"
            onActiveFocusChanged: {
                if (!activeFocus)
                    tableView.closeEditor()
            }
            onValueModified: {
                const res = VariableManage.modifyChildDataType(
                    rowData._key,
                    rowData.dataType,
                    rowData.name,
                    rowData.shortName,
                    value,
                    rowData.description
                )
                if (!res) {
                    messageDialog.show("修改失败")
                } else {
                    editTextChaged(value)
                }
            }
        }
    }

    Component {
        id: description_text_field
        TextField {
            property var rowData: tree_table.getRow(row)

            anchors.fill: parent
            text: String(display)
            font.pixelSize: 14
            maximumLength: 20
            visible: rowData.depth > 1

            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (rowData.description !== text) {
                        const res = VariableManage.modifyChildDataType(
                            rowData._key,
                            rowData.dataType,
                            rowData.name,
                            rowData.shortName,
                            rowData.arrayLength,
                            text
                        )
                        if (!res) {
                            messageDialog.show("修改失败")
                        } else {
                            editTextChaged(text)
                        }
                    }
                    tableView.closeEditor()
                }
            }
        }
    }

    function flatToTree(flatData, id = '_key', parent = 'parentID', children = 'children') {
        // 创建一个映射表，用于快速查找节点
        const nodeMap = new Map();
        const result = [];

        // 第一步：创建所有节点的映射
        flatData.forEach(item => {
            nodeMap.set(item[id], item);
        });

        // 第二步：构建树形结构
        flatData.forEach(item => {
            const node = nodeMap.get(item[id]);
            const parentId = item[parent];

            // 如果是根节点（parentid为0或不存在父节点）
            if (parentId === 0 || !nodeMap.has(parentId)) {
                result.push(node);
            } else {
                // 找到父节点并添加到其children数组中
                const parent = nodeMap.get(parentId);
                if (parent) {
                    if (!parent[children]) {
                        parent[children] = [];
                    }
                    parent[children].push(node);
                }
            }
        })
        return result
    }

    function sortTreeByNumber(tree, sortKey = '_key') {
        return tree.map(node => {
            const sortedNode = node;
            if (sortedNode.children && sortedNode.children.length > 0) {
                sortedNode.children = sortTreeByNumber(sortedNode.children, sortKey)
                    .sort((a, b) => (b[sortKey] || 0) - (a[sortKey] || 0));
            }
            return sortedNode;
        }).sort((a, b) => (a[sortKey] || 0) - (b[sortKey] || 0));
    }

    function getDataBind() {
        type_table.selectIdList = new Set()
        const res = VariableManage.getVariabTypeJsonArray(type_table.deviceName)
        const comboData = VariableManage.getDataType(deviceName).filter(item => {
            return item.deep === 0 && (item.type === "BASE" || item.type === "USER")
        }).map(item => item.name)
        comboData.push('STRUCT')
        type_table.dataTypeList = comboData
        type_table.flatTreeData = res.map(item => {
            return {
                _key: item.vid,
                isSelect: tree_table.customItem(com_checkbox),
                depth: item.deep + 1,
                name: item.name,
                dataType: item.dataType,
                arrayLength: item.arrayCount,
                description: item.description,
                bitLength: item.bitLength,
                mainID: item.mainID,
                mainOffset: item.mainOffset,
                parentID: item.parentID,
                settingName: item.settingName,
                shortName: item.shortName,
                sortNumber: item.sortNumber,
                type: item.type
            }
        })
        tree_table.dataSource = sortTreeByNumber(flatToTree(type_table.flatTreeData))
    }
}