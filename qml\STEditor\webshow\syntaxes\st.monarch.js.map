{"version": 3, "file": "st.monarch.js", "sourceRoot": "", "sources": ["st.monarch.ts"], "names": [], "mappings": "AAAA,mDAAmD;AACnD,eAAe;IACX,QAAQ,EAAE;QACN,KAAK,EAAC,OAAO,EAAC,IAAI,EAAC,MAAM,EAAC,OAAO,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,eAAe,EAAC,UAAU,EAAC,KAAK,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,EAAC,KAAK,EAAC,MAAM,EAAC,WAAW,EAAC,WAAW,EAAC,YAAY,EAAC,UAAU,EAAC,UAAU,EAAC,WAAW,EAAC,WAAW,EAAC,IAAI,EAAC,MAAM,EAAC,eAAe,EAAC,MAAM,EAAC,IAAI,EAAC,IAAI,EAAC,KAAK,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,UAAU,EAAC,mBAAmB,EAAC,SAAS,EAAC,cAAc,EAAC,oBAAoB,EAAC,QAAQ,EAAC,aAAa,EAAC,YAAY,EAAC,cAAc,EAAC,YAAY,EAAC,UAAU,EAAC,SAAS,EAAC,WAAW,EAAC,QAAQ,EAAC,OAAO,EAAC,KAAK,EAAC,UAAU,EAAC,gBAAgB,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,EAAC,KAAK,EAAC,UAAU,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,YAAY,EAAC,KAAK,EAAC,IAAI,EAAC,IAAI,EAAC,IAAI,EAAC,UAAU,EAAC,SAAS,EAAC,WAAW,EAAC,YAAY,EAAC,MAAM,EAAC,QAAQ,EAAC,UAAU,EAAC,QAAQ,EAAC,UAAU,EAAC,IAAI,EAAC,QAAQ,EAAC,QAAQ,EAAC,QAAQ,EAAC,MAAM,EAAC,IAAI,EAAC,QAAQ,EAAC,QAAQ,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,MAAM,EAAC,aAAa,EAAC,IAAI,EAAC,KAAK,EAAC,MAAM,EAAC,KAAK,EAAC,KAAK,EAAC,IAAI,EAAC,MAAM,EAAC,MAAM,EAAC,OAAO,EAAC,MAAM,EAAC,OAAO,EAAC,OAAO,EAAC,OAAO,EAAC,KAAK,EAAC,YAAY,EAAC,YAAY,EAAC,cAAc,EAAC,YAAY,EAAC,WAAW,EAAC,YAAY,EAAC,YAAY,EAAC,UAAU,EAAC,UAAU,EAAC,OAAO,EAAC,MAAM,EAAC,MAAM,EAAC,SAAS,EAAC,KAAK;KACnkC;IACD,SAAS,EAAE;QACP,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,GAAG,EAAC,IAAI,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG,EAAC,IAAI,EAAC,GAAG;KAC7F;IACD,OAAO,EAAE,uEAAuE;IAEhF,SAAS,EAAE;QACP,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,uBAAuB,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,gBAAgB,EAAC,EAAE;YACtE,EAAE,KAAK,EAAE,oDAAoD,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,aAAa,EAAC,EAAE;YAChG,EAAE,KAAK,EAAE,2BAA2B,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,eAAe,EAAC,EAAE;YACzE,EAAE,KAAK,EAAE,mBAAmB,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,oBAAoB,EAAC,EAAE;YACtE,EAAE,KAAK,EAAE,mBAAmB,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,oBAAoB,EAAC,EAAE;YACtE,EAAE,KAAK,EAAE,yCAAyC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,eAAe,EAAC,EAAE;YACvF,EAAE,KAAK,EAAE,qCAAqC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,UAAU,EAAC,EAAE;YAC9E,EAAE,KAAK,EAAE,qCAAqC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,UAAU,EAAC,EAAE;YAC9E,EAAE,KAAK,EAAE,qCAAqC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,OAAO,EAAC,EAAE;YAC3E,EAAE,KAAK,EAAE,iCAAiC,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,QAAQ,EAAC,EAAE;YACxE,EAAE,KAAK,EAAE,GAAG,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE,UAAU,EAAE,EAAC,OAAO,EAAC,YAAY,EAAC,EAAE,EAAC,EAAE;YAC1G,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE,UAAU,EAAE,EAAC,OAAO,EAAC,QAAQ,EAAC,EAAE,EAAC,EAAE;YAC3G,EAAE,KAAK,EAAE,yBAAyB,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE,UAAU,EAAE,EAAC,OAAO,EAAC,aAAa,EAAC,EAAE,EAAC,EAAE;YACjI,EAAE,KAAK,EAAE,WAAW,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,yBAAyB,EAAC,EAAE;YACnE,EAAE,KAAK,EAAE,iBAAiB,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,wBAAwB,EAAC,EAAE;YACxE,EAAE,KAAK,EAAE,SAAS,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE,UAAU,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE,EAAC,EAAE;YAC7G,EAAE,KAAK,EAAE,gBAAgB,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,UAAU,EAAC,EAAE;YACzD,EAAE,KAAK,EAAE,6BAA6B,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,WAAW,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE,UAAU,EAAE,EAAC,OAAO,EAAC,IAAI,EAAC,EAAE,EAAC,EAAE;YAC5H,EAAE,OAAO,EAAE,aAAa,EAAE;YAC1B,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,EAAE,KAAK,EAAE,EAAE,YAAY,EAAE,EAAC,OAAO,EAAC,UAAU,EAAC,EAAE,UAAU,EAAE,EAAC,OAAO,EAAC,EAAE,EAAC,EAAE,EAAC,EAAE;SAC5G;QACD,UAAU,EAAE;YACR,EAAE,KAAK,EAAE,KAAK,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,OAAO,EAAC,EAAE;YAC3C,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,UAAU,EAAC,EAAE;YAChE,EAAE,KAAK,EAAE,cAAc,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE;SACzD;QACD,OAAO,EAAE;YACL,EAAE,KAAK,EAAE,UAAU,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE;YAClD,EAAE,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,MAAM,EAAC,MAAM,EAAC,EAAE;YAC5D,EAAE,KAAK,EAAE,QAAQ,EAAE,MAAM,EAAE,EAAC,OAAO,EAAC,SAAS,EAAC,EAAE;SACnD;KACJ;CACJ,CAAC"}