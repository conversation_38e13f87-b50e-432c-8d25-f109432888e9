
#include "sqlorm.h"
#include "hlog.h"
#include <QxOrm.h>
#include <algorithm>

SqlOrm &SqlOrm::instance()
{
    static SqlOrm instance;
    return instance;
}

// 创建数据库
bool SqlOrm::databaseCreate(const QString &pathName, const QString &password)
{
    QSqlDatabase database;
    // 检查指定的连接(connection)是否存在
    if (QSqlDatabase::contains(pathName))
    {
        // QSqlDatabase::database()返回这个连接
        database = QSqlDatabase::database(pathName);
    }
    else
    {
        // 创建连接
        database = QSqlDatabase::addDatabase("SQLITECIPHER");
        // database.setHostName("127.0.0.1");
        // database.setPort(1874);
        // 设置数据库路径，不存在则创建新数据库
        database.setDatabaseName(pathName);
        //  database.setUserName("root");
        database.setPassword(password);
        database.setConnectOptions("QSQLITE_CREATE_KEY");
    }
    if (!database.open())
    {
        LOG_ERROR_DEFAULT("Error: Failed to connect database.");
        return false;
    }
    else
    {
        qDebug() << "success to connect database.";
    }
    return false;
}

// 链接数据库
bool SqlOrm::databaseConnect(const QString &pathName, const QString &password)
{
    // qInfo() << "pathName: " << pathName << " password: " << password << "\n";
    // 检查数据库已经打开则直接返回
    if (databaseMap.contains(pathName))
    {
        LOG_ERROR_DEFAULT("database already open");
        return true;
    }
    QSqlDatabase database;
    // 检查指定的连接(connection)是否存在

    database = QSqlDatabase::addDatabase("SQLITECIPHER", pathName);
    // database.setHostName("127.0.0.1");
    // database.setPort(1874);
    database.setDatabaseName(pathName);
    // database.setUserName("root");
    database.setPassword(password);

    qx::QxSqlDatabase *pDatabaseSettings = qx::QxSqlDatabase::getSingleton();
    pDatabaseSettings->setTraceSqlQuery(false);
    // 打开数据库并检查是否成功
    if (!database.open())
    {
        QSqlError error = database.lastError();
        LOG_ERROR_DEFAULT("Error: Failed to connect database." + error.text().toStdString());
        return false;
    }

    databaseMap.insert(pathName, database);
    return true;
}

// 获取数据库实例
QSqlDatabase &SqlOrm::getDatabase(const QString &pathName)
{
    return databaseMap[pathName];
}

// 关闭数据库
void SqlOrm::closeDatabase(const QString &pathName)
{
    // qInfo() << __FILE__ << __func__ << __LINE__ << "pathName: " << pathName;
    databaseMap[pathName].close();
    QSqlDatabase::removeDatabase(pathName);
    databaseMap.remove(pathName);
}

void SqlOrm::closeAllDatabase()
{
    // 关闭所有数据库
    for (auto it = databaseMap.begin(); it != databaseMap.end(); ++it)
    {
        it.value().close();
    }
    databaseMap.clear();
}

void SqlOrm::closePrintInfo()
{
    // 关闭所有QxOrm打印信息
    qx::QxSqlDatabase *pDatabaseSettings = qx::QxSqlDatabase::getSingleton();

    // 关闭SQL查询跟踪
    pDatabaseSettings->setTraceSqlQuery(false);

    // 关闭SQL记录跟踪
    pDatabaseSettings->setTraceSqlRecord(false);

    // 关闭SQL绑定值跟踪
    pDatabaseSettings->setTraceSqlBoundValues(false);

    // 关闭错误时的SQL绑定值跟踪
    pDatabaseSettings->setTraceSqlBoundValuesOnError(false);

    // 关闭计时器详细信息显示
    pDatabaseSettings->setDisplayTimerDetails(false);
}