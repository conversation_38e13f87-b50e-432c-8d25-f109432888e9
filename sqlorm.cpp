
#include "sqlorm.h"
#include "hlog.h"
#include <QxOrm.h>
#include <algorithm>

SqlOrm &SqlOrm::instance()
{
    static SqlOrm instance;
    return instance;
}

// 创建数据库
bool SqlOrm::databaseCreate(const QString &pathName, const QString &password)
{
    QSqlDatabase database;
    // 检查指定的连接(connection)是否存在
    if (QSqlDatabase::contains(pathName))
    {
        // QSqlDatabase::database()返回这个连接
        database = QSqlDatabase::database(pathName);
    }
    else
    {
        // 创建连接
        database = QSqlDatabase::addDatabase("SQLITECIPHER");
        // database.setHostName("127.0.0.1");
        // database.setPort(1874);
        // 设置数据库路径，不存在则创建新数据库
        database.setDatabaseName(pathName);
        //  database.setUserName("root");
        database.setPassword(password);
        database.setConnectOptions("QSQLITE_CREATE_KEY");
    }
    if (!database.open())
    {
        LOG_ERROR_DEFAULT("Error: Failed to connect database.");
        return false;
    }
    else
    {
        qDebug() << "success to connect database.";

        // 配置QxOrm数据库设置，关闭所有日志输出
        qx::QxSqlDatabase *pDatabaseSettings = qx::QxSqlDatabase::getSingleton();

        // 全局设置
        pDatabaseSettings->setTraceSqlQuery(false);
        pDatabaseSettings->setTraceSqlRecord(false);
        pDatabaseSettings->setTraceSqlBoundValues(false);
        pDatabaseSettings->setTraceSqlBoundValuesOnError(false);
        pDatabaseSettings->setDisplayTimerDetails(false);

        // 针对当前线程的设置
        pDatabaseSettings->setTraceSqlQuery(false, true);
        pDatabaseSettings->setTraceSqlRecord(false, true);
        pDatabaseSettings->setTraceSqlBoundValues(false, true);
        pDatabaseSettings->setTraceSqlBoundValuesOnError(false, true);
        pDatabaseSettings->setDisplayTimerDetails(false, true);

        // 针对特定数据库连接的设置
        pDatabaseSettings->setTraceSqlQuery(false, false, &database);
        pDatabaseSettings->setTraceSqlRecord(false, false, &database);
        pDatabaseSettings->setTraceSqlBoundValues(false, false, &database);
        pDatabaseSettings->setTraceSqlBoundValuesOnError(false, false, &database);
        pDatabaseSettings->setDisplayTimerDetails(false, false, &database);
    }
    return true;
}

// 链接数据库
bool SqlOrm::databaseConnect(const QString &pathName, const QString &password)
{
    // qInfo() << "pathName: " << pathName << " password: " << password << "\n";
    // 检查数据库已经打开则直接返回
    if (databaseMap.contains(pathName))
    {
        LOG_ERROR_DEFAULT("database already open");
        return true;
    }
    QSqlDatabase database;
    // 检查指定的连接(connection)是否存在

    database = QSqlDatabase::addDatabase("SQLITECIPHER", pathName);
    // database.setHostName("127.0.0.1");
    // database.setPort(1874);
    database.setDatabaseName(pathName);
    // database.setUserName("root");
    database.setPassword(password);

    // 配置QxOrm数据库设置，关闭所有日志输出
    qx::QxSqlDatabase *pDatabaseSettings = qx::QxSqlDatabase::getSingleton();

    // 全局设置
    pDatabaseSettings->setTraceSqlQuery(false);
    pDatabaseSettings->setTraceSqlRecord(false);
    pDatabaseSettings->setTraceSqlBoundValues(false);
    pDatabaseSettings->setTraceSqlBoundValuesOnError(false);
    pDatabaseSettings->setDisplayTimerDetails(false);

    // 针对当前线程的设置
    pDatabaseSettings->setTraceSqlQuery(false, true);
    pDatabaseSettings->setTraceSqlRecord(false, true);
    pDatabaseSettings->setTraceSqlBoundValues(false, true);
    pDatabaseSettings->setTraceSqlBoundValuesOnError(false, true);
    pDatabaseSettings->setDisplayTimerDetails(false, true);
    // 打开数据库并检查是否成功
    if (!database.open())
    {
        QSqlError error = database.lastError();
        LOG_ERROR_DEFAULT("Error: Failed to connect database." + error.text().toStdString());
        return false;
    }

    // 针对特定数据库连接的设置
    pDatabaseSettings->setTraceSqlQuery(false, false, &database);
    pDatabaseSettings->setTraceSqlRecord(false, false, &database);
    pDatabaseSettings->setTraceSqlBoundValues(false, false, &database);
    pDatabaseSettings->setTraceSqlBoundValuesOnError(false, false, &database);
    pDatabaseSettings->setDisplayTimerDetails(false, false, &database);

    databaseMap.insert(pathName, database);
    return true;
}

// 获取数据库实例
QSqlDatabase &SqlOrm::getDatabase(const QString &pathName)
{
    return databaseMap[pathName];
}

// 关闭数据库
void SqlOrm::closeDatabase(const QString &pathName)
{
    // qInfo() << __FILE__ << __func__ << __LINE__ << "pathName: " << pathName;
    databaseMap[pathName].close();
    QSqlDatabase::removeDatabase(pathName);
    databaseMap.remove(pathName);
}

void SqlOrm::closeAllDatabase()
{
    // 关闭所有数据库
    for (auto it = databaseMap.begin(); it != databaseMap.end(); ++it)
    {
        it.value().close();
    }
    databaseMap.clear();
}

void SqlOrm::closePrintInfo()
{
    // 关闭所有QxOrm打印信息
    qx::QxSqlDatabase *pDatabaseSettings = qx::QxSqlDatabase::getSingleton();

    // 全局设置
    pDatabaseSettings->setTraceSqlQuery(false);
    pDatabaseSettings->setTraceSqlRecord(false);
    pDatabaseSettings->setTraceSqlBoundValues(false);
    pDatabaseSettings->setTraceSqlBoundValuesOnError(false);
    pDatabaseSettings->setDisplayTimerDetails(false);

    // 针对当前线程的设置
    pDatabaseSettings->setTraceSqlQuery(false, true);
    pDatabaseSettings->setTraceSqlRecord(false, true);
    pDatabaseSettings->setTraceSqlBoundValues(false, true);
    pDatabaseSettings->setTraceSqlBoundValuesOnError(false, true);
    pDatabaseSettings->setDisplayTimerDetails(false, true);

    // 针对所有已连接的数据库设置
    for (auto it = databaseMap.begin(); it != databaseMap.end(); ++it)
    {
        QSqlDatabase &db = it.value();
        pDatabaseSettings->setTraceSqlQuery(false, false, &db);
        pDatabaseSettings->setTraceSqlRecord(false, false, &db);
        pDatabaseSettings->setTraceSqlBoundValues(false, false, &db);
        pDatabaseSettings->setTraceSqlBoundValuesOnError(false, false, &db);
        pDatabaseSettings->setDisplayTimerDetails(false, false, &db);
    }
}