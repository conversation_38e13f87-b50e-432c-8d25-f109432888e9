import QtQuick 2.15
import QtQuick.Controls 2.15
import QtQuick.Layouts 1.15
import Qt.labs.qmlmodels 1.0
import SimpleUI 1.0
import "qrc:/SimpleUI/qml"

Rectangle {
    id: control

    property var dataSource
    property var columnSource: []
    property int cellHeight: 30
    property int depthPadding: 10
    property bool showLine: false
    property bool checkable: false
    property color lineColor: Qt.rgba(210 / 255, 210 / 255, 210 / 255, 1)
    property color borderColor: Qt.rgba(228 / 255, 228 / 255, 228 / 255, 1)
    property color selectedBorderColor: Qt.rgba(187 / 255, 222 / 255, 251 / 255, 1)
    property color selectedColor: Qt.rgba(187 / 255, 222 / 255, 251 / 255, 0.3)
    readonly property alias current: d.current
    property alias view: table_view
    property color customLineColor
    property var customLineColorIndexs: []

    color: Qt.rgba(255 / 255, 255 / 255, 255 / 255, 0.8)

    signal clicked(int row, int column)

    signal doubleClicked(int row, int column)

    onDataSourceChanged: {
        tree_model.setDataSource(dataSource)
    }

    onColumnSourceChanged: {
        if (columnSource.length !== 0) {
            const columns = []
            const headerRow = {}
            columnSource.forEach(function (item) {
                const column = Qt.createQmlObject(
                    'import Qt.labs.qmlmodels 1.0;TableModelColumn{}',
                    control)
                column.display = item.dataIndex
                columns.push(column)
                headerRow[item.dataIndex] = item.title
            })
            header_column_model.columns = columns
            header_column_model.rows = [headerRow]
        }
    }

    onWidthChanged: {
        forceLayout()
    }

    onDepthPaddingChanged: {
        table_view.forceLayout()
    }

    onCellHeightChanged: {
        table_view.forceLayout()
    }

    onCheckableChanged: {
        delay_force_layout.restart()
    }

    Timer {
        id: delay_force_layout
        interval: 30
        onTriggered: {
            table_view.forceLayout()
        }
    }

    QtObject {
        id: d
        property var current
        property int defaultItemWidth: 100
        property int rowHoverIndex: -1
        property var editDelegate
        property var editPosition

        function getEditDelegate(column) {
            const obj = control.columnSource[column].editDelegate
            if (obj) {
                return obj
            }
            return com_edit
        }
    }

    TableModel {
        id: header_column_model
        TableModelColumn {
        }
    }

    TreeModel {
        id: tree_model
        columnSource: control.columnSource
    }

    // 表格头部视图
    TableView {
        id: header_horizontal
        height: Math.max(1, contentHeight)
        anchors {
            left: table_view.left
            right: table_view.right
            top: parent.top
        }
        model: header_column_model
        boundsBehavior: Flickable.StopAtBounds
        clip: true
        reuseItems: true
        syncDirection: Qt.Horizontal
        columnWidthProvider: table_view.columnWidthProvider
        syncView: table_view.rows === 0 ? null : table_view
        onContentXChanged: {
            timer_horizontal_force_layout.restart()
        }

        Timer {
            id: timer_horizontal_force_layout
            interval: 30
            onTriggered: {
                header_horizontal.forceLayout()
            }
        }

        delegate: com_column_header_delegate
    }

    // 表格主视图
    TableView {
        id: table_view
        anchors {
            top: header_horizontal.bottom
            left: parent.left
            right: parent.right
            bottom: parent.bottom
        }
        boundsBehavior: Flickable.StopAtBounds
        clip: true
        model: tree_model

        columnWidthProvider: column => {
            const columnObject = control.columnSource[column]
            const width = columnObject.width
            const hide = columnObject.hide
            const minimumWidth = columnObject.minimumWidth
            if (hide)
                return 0
            if (width)
                return width
            if (minimumWidth)
                return minimumWidth
            return d.defaultItemWidth
        }

        rowHeightProvider: row => {
            return control.cellHeight
        }

        ScrollBar.horizontal: ScrollBar {
            policy: ScrollBar.AsNeeded
        }

        ScrollBar.vertical: ScrollBar {
            policy: ScrollBar.AsNeeded
        }

        delegate: MouseArea {
            id: item_table
            implicitHeight: control.cellHeight
            implicitWidth: 100
            hoverEnabled: true

            property var rowObject: rowModel.data
            property alias isRowSelected: item_table_loader.isRowSelected
            property var display: rowModel.data[columnModel.dataIndex]
            property bool isObject: typeof (item_table.display) == "object"
            property bool editVisible: {
                return !!(rowObject && d.editPosition
                    && d.editPosition._key === rowObject._key
                    && d.editPosition.column === column)
            }

            function updateEditPosition() {
                const obj = {}
                obj._key = rowObject._key
                obj.column = column
                obj.row = row
                obj.x = item_table.x
                obj.y = item_table.y + 1
                obj.width = item_table.width
                obj.height = item_table.height - 2
                d.editPosition = obj
            }

            onEntered: {
                d.rowHoverIndex = row
            }

            onWidthChanged: {
                if (editVisible) {
                    updateEditPosition()
                }
            }

            onHeightChanged: {
                if (editVisible) {
                    updateEditPosition()
                }
            }

            onXChanged: {
                if (editVisible) {
                    updateEditPosition()
                }
            }

            onYChanged: {
                if (editVisible) {
                    updateEditPosition()
                }
            }

            onDoubleClicked: {
                if (typeof (display) == "object"
                    || model.columnModel.readOnly) {

                    control.doubleClicked(model.row, model.column)
                    return
                }
                d.editDelegate = d.getEditDelegate(column)
                updateEditPosition()
                loader_edit.display = display
                loader_edit.item.forceActiveFocus()
            }

            onClicked: event => {
                control.clicked(model.row, model.column)
                d.current = rowModel
                focus = true
                event.accepted = true
            }

            Rectangle {
                anchors.fill: parent
                color: {
                    if (control.customLineColorIndexs.includes(rowObject._key)) {
                        return control.customLineColor
                    }
                    if (item_table.isRowSelected) {
                        return control.selectedColor
                    }
                    if (d.rowHoverIndex === row || item_table.isRowSelected) {
                        return Qt.rgba(0, 0, 0, 0.06)
                    }
                    return (row % 2 !== 0) ? control.color : Qt.rgba(0, 0, 0, 0.015)
                }

                Item {
                    anchors.fill: parent

                    Rectangle {
                        width: 1
                        height: parent.height
                        anchors.left: parent.left
                        color: control.borderColor
                    }

                    Rectangle {
                        width: 1
                        height: parent.height
                        anchors.right: parent.right
                        color: control.borderColor
                        visible: column === control.columnSource.length - 1
                    }

                    Rectangle {
                        width: parent.width
                        height: 1
                        anchors.top: parent.top
                        color: control.borderColor
                        visible: row === 0
                    }

                    Rectangle {
                        width: parent.width
                        height: 1
                        anchors.bottom: parent.bottom
                        color: item_table.isRowSelected ? control.selectedBorderColor : control.borderColor
                    }
                }
            }

            Loader {
                id: item_table_loader
                anchors.fill: parent
                property var rowModel: model.rowModel
                property var columnModel: model.columnModel
                property int row: model.row
                property int column: model.column
                property var display: item_table.display
                property var tableView: table_view
                property bool isRowSelected: d.current === rowModel
                property var options: {
                    if (isObject) {
                        return display.options
                    }
                    return {}
                }

                active: rowModel !== undefined && rowModel !== null
                sourceComponent: {
                    if (column === 0)
                        return com_column
                    if (item_table.isObject) {
                        return display.comId
                    }
                    return com_other
                }
            }
        }
        Loader {
            id: loader_edit
            property var tableView: control
            property var display
            property int column: {
                if (d.editPosition) {
                    return d.editPosition.column
                }
                return 0
            }
            property int row: {
                if (d.editPosition) {
                    return d.editPosition.row
                }
                return 0
            }

            signal editTextChaged(string text)

            sourceComponent: d.editPosition ? d.editDelegate : undefined
            onEditTextChaged: text => {
                const obj = tree_model.getRow(row).data
                obj[control.columnSource[column].dataIndex] = text
                tree_model.setRow(row, obj)
            }
            width: {
                if (d.editPosition) {
                    return d.editPosition.width
                }
                return 0
            }
            height: {
                if (d.editPosition) {
                    return d.editPosition.height
                }
                return 0
            }
            x: {
                if (d.editPosition) {
                    return d.editPosition.x
                }
                return 0
            }
            y: {
                if (d.editPosition) {
                    return d.editPosition.y
                }
                return 0
            }
            z: 999
        }
    }

    // 列头代理
    Component {
        id: com_column_header_delegate
        Rectangle {
            id: column_item_control
            readonly property real cellPadding: 8
            property bool canceled: false
            property int columnIndex: column
            property var columnObject: control.columnSource[column]
            implicitWidth: {
                return (item_column_loader.item
                    && item_column_loader.item.implicitWidth) + (cellPadding * 2)
            }
            implicitHeight: Math.max(
                36,
                (item_column_loader.item
                    && item_column_loader.item.implicitHeight) + (cellPadding * 2))
            color: Qt.rgba(247 / 255, 247 / 255, 247 / 255, 1)
            Rectangle {
                border.color: control.borderColor
                width: parent.width
                height: 1
                anchors.top: parent.top
                color: "#00000000"
            }
            Rectangle {
                border.color: control.borderColor
                width: parent.width
                height: 1
                anchors.bottom: parent.bottom
                color: "#00000000"
            }
            Rectangle {
                border.color: control.borderColor
                width: 1
                height: parent.height
                anchors.left: parent.left
                color: "#00000000"
            }
            Rectangle {
                border.color: control.borderColor
                width: 1
                height: parent.height
                anchors.right: parent.right
                color: "#00000000"
                visible: column === table_view.columns - 1
            }
            MouseArea {
                id: column_item_control_mouse
                anchors.fill: parent
                anchors.rightMargin: 6
                hoverEnabled: true
                onCanceled: {
                    column_item_control.canceled = true
                }
                onContainsMouseChanged: {
                    if (!containsMouse) {
                        column_item_control.canceled = false
                    }
                }
                onClicked: event => {
                    focus = true
                }
            }
            Loader {
                id: item_column_loader
                property var itemModel: model
                property var modelData: model.display
                property var tableView: table_view
                property var options: {
                    if (typeof (modelData) == "object") {
                        return modelData.options
                    }
                    return {}
                }
                property int column: column_item_control.columnIndex
                width: parent.width
                height: parent.height
                sourceComponent: {
                    if (typeof (modelData) == "object") {
                        return modelData.comId
                    }
                    return com_column_text
                }
            }
            MouseArea {
                property point clickPos: "0,0"
                height: parent.height
                width: 6
                anchors.right: parent.right
                acceptedButtons: Qt.LeftButton
                hoverEnabled: true
                visible: !(columnObject.width === columnObject.minimumWidth
                    && columnObject.width === columnObject.maximumWidth
                    && columnObject.width)
                cursorShape: Qt.SplitHCursor
                preventStealing: true
                onPressed: mouse => {
                    clickPos = Qt.point(mouse.x, mouse.y)
                }
                onPositionChanged: mouse => {
                    if (!pressed) {
                        return
                    }
                    const delta = Qt.point(
                        mouse.x - clickPos.x,
                        mouse.y - clickPos.y)
                    let minimumWidth = columnObject.minimumWidth
                    let maximumWidth = columnObject.maximumWidth
                    let w = columnObject.width
                    if (!w) {
                        w = d.defaultItemWidth
                    }
                    if (!minimumWidth) {
                        minimumWidth = d.defaultItemWidth
                    }
                    if (!maximumWidth) {
                        maximumWidth = 65535
                    }
                    columnObject.width = Math.min(
                        Math.max(minimumWidth, w + delta.x),
                        maximumWidth)
                    table_view.forceLayout()
                }
            }
        }
    }

    // 头部单元格文本代理
    Component {
        id: com_column_text
        Text {
            id: column_text
            text: modelData
            anchors.fill: parent
            font.pixelSize: 14
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
        }
    }

    // 第一列单元格代理
    Component {
        id: com_column
        Item {
            id: item_container
            clip: true

            function toggle() {
                if (rowModel.expanded) {
                    tree_model.collapse(row)
                } else {
                    tree_model.expand(row)
                }
                delay_force_layout.restart()
            }

            Rectangle {
                width: 1
                color: control.lineColor
                visible: control.showLine && rowModel.depth !== 0&& !rowModel.hasChildren()
                height: rowModel.hideLineFooter() ? parent.height / 2 : parent.height
                anchors {
                    top: parent.top
                    right: item_line_h.left
                }
            }

            Rectangle {
                id: item_line_h
                height: 1
                color: control.lineColor
                visible: control.showLine && rowModel.depth !== 0
                    && !rowModel.hasChildren()
                width: 4
                anchors {
                    right: layout_row.left
                    rightMargin: control.depthPadding - 15
                    verticalCenter: parent.verticalCenter
                }
            }

            Repeater {
                model: {
                    return Math.max(rowModel.depth - 1, 0)
                }
                delegate: Rectangle {
                    required property int index
                    width: 1
                    color: control.lineColor
                    visible: control.showLine && rowModel.depth !== 0
                        && rowModel.hasNextNodeByIndex(index)
                    anchors {
                        top: parent.top
                        bottom: parent.bottom
                        left: parent.left
                        leftMargin: control.depthPadding * index + 10
                    }
                }
            }

            RowLayout {
                id: layout_row
                height: parent.height
                anchors.fill: parent
                spacing: 0
                anchors.leftMargin: control.depthPadding * rowModel.depth

                Component {
                    id: com_icon_btn
                    Item {
                        Image {
                            anchors.centerIn: parent
                            rotation: rowModel.expanded ? 90 : 0
                            opacity: rowModel.hasChildren()
                            source: "qrc:/assets/icon/slices/right_arrow.svg"
                        }
                        MouseArea {
                            anchors.fill: parent
                            cursorShape: Qt.PointingHandCursor
                            onClicked: {
                                item_container.toggle()
                                focus = true
                            }
                        }
                    }
                }

                Loader {
                    id: item_loader_expand
                    Layout.preferredWidth: 20
                    Layout.preferredHeight: 20
                    sourceComponent: rowModel.hasChildren() ? com_icon_btn : undefined
                    Layout.alignment: Qt.AlignVCenter
                }

                Item {
                    Layout.fillHeight: true
                    Layout.fillWidth: true
                    Layout.leftMargin: 6
                    Text {
                        id: item_text
                        text: JSON.stringify(display)
                        elide: Text.ElideRight
                        font.pixelSize: 14
                        verticalAlignment: Text.AlignVCenter
                        anchors.fill: parent
                        MouseArea {
                            id: hover_handler
                            acceptedButtons: Qt.NoButton
                            hoverEnabled: true
                            anchors.fill: parent
                        }
                    }
                }
            }
        }
    }

    // 其他单元格文本代理(除了第一列)
    Component {
        id: com_other
        Text {
            id: item_text
            text: String(display)
            elide: Text.ElideRight
            anchors {
                fill: parent
                leftMargin: 11
                rightMargin: 11
                topMargin: 6
                bottomMargin: 6
            }
            font.pixelSize: 14
            horizontalAlignment: Text.AlignHCenter
            verticalAlignment: Text.AlignVCenter
            MouseArea {
                acceptedButtons: Qt.NoButton
                id: hover_handler
                hoverEnabled: true
                anchors.fill: parent
            }
        }
    }

    // 编辑单元格代理
    Component {
        id: com_edit
        TextField {
            id: text_box
            text: String(display)
            readOnly: true === control.columnSource[column].readOnly
            Component.onCompleted: {
                forceActiveFocus()
                selectAll()
            }
            Keys.onReturnPressed: {
                focus = false
            }
            Keys.onEnterPressed: {
                focus = false
            }
            onActiveFocusChanged: {
                if (!activeFocus) {
                    if (!readOnly) {
                        editTextChaged(text_box.text)
                    }
                    control.closeEditor()
                }
            }
        }
    }

    function count() {
        return tree_model.dataSourceSize
    }

    function visibleCount() {
        return table_view.rows
    }

    function collapse(rowIndex) {
        tree_model.collapse(rowIndex)
    }

    function expand(rowIndex) {
        tree_model.expand(rowIndex)
    }

    function allExpand() {
        tree_model.allExpand()
    }

    function allCollapse() {
        tree_model.allCollapse()
    }

    function customItem(comId, options = {}) {
        const o = {}
        o.comId = comId
        o.options = options
        return o
    }

    function closeEditor() {
        d.editPosition = undefined
        d.editDelegate = undefined
    }

    function selectionModel() {
        return tree_model.selectionModel()
    }

    function setRow(rowIndex, obj) {
        const data = getRow(rowIndex)
        const merged = Object.assign({}, data, obj)
        tree_model.setRow(rowIndex, merged)
    }

    function getRow(rowIndex) {
        if (rowIndex >= 0 && rowIndex < table_view.rows) {
            return tree_model.getRow(rowIndex).data
        }
        return null
    }

    function checkRow(rowIndex, checked) {
        tree_model.checkRow(rowIndex, checked)
    }

    function checkAll(checked) {
        for (var i = 0; i < count(); i++) {
            checkRow(i, checked)
        }
    }

    function forceLayout() {
        timer_horizontal_force_layout.restart()
        delay_force_layout.restart()
    }

    Component.onCompleted: {
        forceLayout()
    }

    Component.onDestruction: {
        table_view.contentY = 0
    }
}
