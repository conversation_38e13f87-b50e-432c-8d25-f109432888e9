﻿#include "businessmanage.h"
#include "tcpservice.h"
#include "IDEProjectAndFile/projectandfilemanage.h"
#include <QRandomGenerator>
#include "commserv.h"
#include <QNetworkAccessManager>
#include <QNetworkReply>
#include "udpservice.h"
#include "sqlorm.h"
#include <QCryptographicHash>
#include "deviceandnetworkmanage.h"
TcpBusiness3568::TcpBusiness3568():
    onlineFlag(false),
    lastHeartTime(0),
    runningFlag(false)
{
    connect(&heartTimer, &QTimer::timeout, this, &TcpBusiness3568::heartBeat);
    connect(this, &TcpBusiness3568::heartBeatStart, this, &TcpBusiness3568::heartBeatStart_slot, Qt::QueuedConnection);
    connect(this, &TcpBusiness3568::stopHeartTimer, &heartTimer, &QTimer::stop, Qt::QueuedConnection);
}

TcpBusiness3568 &TcpBusiness3568::instance()
{
    static TcpBusiness3568 instance;
    return instance;
}

void TcpBusiness3568::heartBeatStart_slot()
{
    onlineFlag = true;
    lastHeartTime = QDateTime::currentMSecsSinceEpoch();
    heartTimer.start(1000);
}

void TcpBusiness3568::compressFolder(const QString &sourceFolder, const QString &zipFilePath)
{
    QDir dir(sourceFolder);
    if (!dir.exists()) {
        qWarning() << "Source folder does not exist:" << sourceFolder;
        return;
    }

    QZipWriter zipWriter(zipFilePath);
    compressDirectory(&zipWriter, sourceFolder, sourceFolder);
    zipWriter.close();
}

void TcpBusiness3568::compressDirectory(QZipWriter *zipWriter, const QString &currentPath, const QString &rootPath)
{
    QDir dir(currentPath);
    QFileInfoList fileList = dir.entryInfoList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);
    for (const QFileInfo &fileInfo : fileList) {
        QString relativePath = fileInfo.filePath().mid(rootPath.length() + 1);

        // // 跳过 "WavePersistent" 文件夹
        // if (fileInfo.fileName() == "WavePersistent") {
        //     continue;
        // }
        if(upPro_skipFileDir.contains(fileInfo.fileName())){
            continue;
        }

        if (fileInfo.isDir()) {
            compressDirectory(zipWriter, fileInfo.filePath(), rootPath);
        } else {
            QFile file(fileInfo.filePath());
            if (file.open(QIODevice::ReadOnly)) {
                zipWriter->addFile(relativePath, &file);
                file.close();
            } else {
                qWarning() << "Failed to open file:" << fileInfo.filePath();
            }
        }
    }
}

void TcpBusiness3568::encryption(const QString &source, QByteArray &target, const QByteArray &key)
{
    if (key.isEmpty()) {
        qWarning() << "Key is empty!";
        return;
    }

    // QByteArray head = generateRandomByteArray(129);
    target.clear();
    target.append(generateRandomByteArray(129));

    // QFile outputFile(target);
    // if (!outputFile.open(QIODevice::WriteOnly)) {
    //     qDebug() << "Failed to open output file for writing:" << target;
    //     return;
    // }

    // outputFile.write(head);

    quint8 start = target.at(88) % key.size();

    QFile inputFile(source);
    if (!inputFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open input file for reading:" << source;
        // outputFile.close();
        return;
    }

    QByteArray fileData = inputFile.readAll();
    qDebug() << "inputFile size" << fileData.size();
    QByteArray encryptedData(fileData.size(), 0x00);

    for (int i = 0; i < fileData.size(); ++i) {
        int keyIndex = (start + i) % key.size();
        encryptedData[i] = fileData[i] ^ key[keyIndex];
    }
    qDebug() << "encryptedData size" << encryptedData.size();

    target.append(encryptedData);

    inputFile.close();
    // outputFile.close();

    qDebug() << "Encryption completed successfully.";
}

bool TcpBusiness3568::decryption(const QByteArray &encryptedFile, const QByteArray &key, QByteArray &decryptedData)
{
    // 检查文件长度是否足够
    if (encryptedFile.size() < 129) { // 假设头部长度是129字节
        qWarning() << "Encrypted file is too short!";
        return false;
    }

    // 分离头部和加密数据
    QByteArray head = encryptedFile.left(129);
    QByteArray encryptedData = encryptedFile.mid(129);

    // 计算密钥起始位置
    quint8 start = head.at(88) % key.size();

    // 解密数据
    decryptedData.resize(encryptedData.size());
    for (int i = 0; i < encryptedData.size(); ++i) {
        int keyIndex = (start + i) % key.size();
        decryptedData[i] = encryptedData[i] ^ key[keyIndex];
    }
    return true;
}

void TcpBusiness3568::downloadFileList(httpReplyFunc func)
{
    QString path;
    if(filePaths.size() > 0){
        path = filePaths.last();
        filePaths.removeLast();
    }else{
        if(func == httpReplyFunc::UploadFileList_RAM){
            DownloadProjectInfo_Ram();
        }
        else if(func == httpReplyFunc::UploadFileList_Flash){
            DownloadProjectInfo_Flash();
        }
    }
    QFile file(path);
    if(file.open(QIODevice::ReadOnly)){
        qDebug() << "Failed to open input file for reading:" << path;
        emit CommServ::instance().buttonStatus(1, 2, "");
        return;
    }
    qDebug() << "[info]update File Path:" << path << "func:" << (int)func;
    QByteArray bytes = file.readAll();
    QString url = "http://";
    url.append(TcpClientInfos.at(0).remoteAddress);
    url.append(":8081/upload/e2000/");
    QString filename = file.fileName();
    http.uplaodDataPost(url, bytes, filename, func);
}

QByteArray TcpBusiness3568::generateRandomByteArray(int length)
{
    QByteArray randomByteArray(length, 0x00);
    for (int i = 0; i < length; ++i) {
        randomByteArray[i] = static_cast<char>(QRandomGenerator::global()->bounded(0, 256));
    }
    return randomByteArray;
}

bool TcpBusiness3568::uploadProject(QByteArray &data, bool configFlag)
{
    QString zipFilePath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\projectzip.zip";

    // 解密
    QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open file: " << keyPath;
        // writeOldZip(zipFilePath);
        return false;
    }
    QByteArray key = keyFile.readAll();
    QByteArray bytes;
    if(!decryption(data, key, bytes)){
        // writeOldZip(zipFilePath);
        return false;
    }
    QBuffer buffer(&bytes);
    buffer.open(QIODevice::ReadOnly);

    // QString deviceName = ProjectAndFileManage::instance().getCurrentDeviceName();
    QString oldDatabase = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/oldDatabase";
    QZipReader zipreader(&buffer);
    // 先找到数据库文件
    for(auto fileInfo : zipreader.fileInfoList()){
        QString filePath = fileInfo.filePath;
        if(filePath.contains("data") && filePath.contains("manage.db")){
            // QString targetFilePath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "/oldDatabase";
            if(fileInfo.isFile){
                QDir targetDir(QFileInfo(oldDatabase).absolutePath());
                if (!targetDir.exists()) {
                    targetDir.mkpath(".");
                }
                QFile file(oldDatabase);
                if (file.open(QIODevice::WriteOnly)) {
                    file.write(zipreader.fileData(filePath.toUtf8()));
                    file.close();
                } else {
                    qDebug() << "Failed to open file for writing:" << oldDatabase;
                    return false;
                }
            }
            break;
        }
    }

    QFile oldFiledb(oldDatabase);
    if (!oldFiledb.exists())
    {
        qDebug() << "file: " << oldDatabase << "don't exist";
        // writeOldZip(zipFilePath);
        return false;
    }
    SqlOrm::instance().databaseConnect(oldDatabase, "Password123.");
    QSqlDatabase oldDb = SqlOrm::instance().getDatabase(oldDatabase);
    if (!oldDb.open()) {
        qWarning() << "Cannot open database:" << oldDb.lastError();
        // writeOldZip(zipFilePath);
        return false; // 返回空数据库对象
    }

    // 操作数据库
    QString dbName = TcpBusiness3568::instance().uploadExeAndConfigPath + "/data/manage.db";
    QFile filedb(dbName);
    if (!filedb.exists())
    {
        qDebug() << "file: " << dbName << "don't exist";
        writeOldZip(zipFilePath);
        return false;
    }
    SqlOrm::instance().databaseConnect(dbName, "Password123.");
    QSqlDatabase db = SqlOrm::instance().getDatabase(dbName);
    if (!db.open()) {
        qWarning() << "Cannot open database:" << db.lastError();
        writeOldZip(zipFilePath);
        return false; // 返回空数据库对象
    }

    QString oldDeviceName = dao.selectDevicename(oldDb);
    QString deviceName = dao.selectDevicename(db);

    // TcpBusiness3568::instance().uploadExeAndConfigPath + "/" + deviceName + "/User";
    removeDirectory(TcpBusiness3568::instance().uploadExeAndConfigPath + "/" + deviceName + "/User");
    for(auto fileInfo : zipreader.fileInfoList()){
        QString filePath = fileInfo.filePath;
        if((filePath.contains("User") && filePath.contains(oldDeviceName)) ||
            (filePath.contains("oneos.bin") && filePath.contains(oldDeviceName))){
            QStringList pathParts = filePath.split(QRegExp("[/\\\\]"));
            pathParts.first() = deviceName;
            QString newPath = pathParts.join("/");
            QString targetFilePath = TcpBusiness3568::instance().uploadExeAndConfigPath + "/" + newPath;
            if(fileInfo.isFile){
                // 创建目标文件所在的目录
                QDir targetDir(QFileInfo(targetFilePath).absolutePath());
                if (!targetDir.exists()) {
                    targetDir.mkpath(".");
                }
                QFile file(targetFilePath);
                if (file.open(QIODevice::WriteOnly)) {
                    file.write(zipreader.fileData(filePath.toUtf8()));
                    file.close();
                } else {
                    qDebug() << "Failed to open file for writing:" << targetFilePath;
                    return false;
                }
            }
        }
    }

    dao.replaceVariableListTable(oldDb, db, oldDeviceName, deviceName);
    dao.replaceProjectTree(oldDb, db , oldDeviceName, deviceName);
    dao.replaceFileListTable(oldDb, db, oldDeviceName, deviceName);
    dao.replaceVariableType(oldDb, db, oldDeviceName, deviceName);

    if(configFlag){
        dao.replaceDCAndDSAndDVS(oldDb, db, oldDeviceName, deviceName);
        dao.replaceVariableList(oldDb, db, oldDeviceName, deviceName);
    }

    SqlOrm::instance().closeDatabase(oldDatabase);
    SqlOrm::instance().closeDatabase(dbName);
    return true;
}

void TcpBusiness3568::writeOldZip(QString zipFilePath)
{
    QByteArray arrays;
    QFile oldZip(zipFilePath);
    if (oldZip.open(QIODevice::ReadOnly)) {
        arrays = oldZip.readAll();
        oldZip.close();
        decompress(arrays);
    } else {
        qDebug() << "Failed to open file:" << zipFilePath;
    }
}

bool TcpBusiness3568::decompress(QByteArray bytes)
{
    QBuffer buffer(&bytes);
    buffer.open(QIODevice::ReadOnly);

    QZipReader zipreader(&buffer);
    for(auto fileInfo : zipreader.fileInfoList()){
        QString filePath = fileInfo.filePath;
        QString targetFilePath = TcpBusiness3568::instance().uploadExeAndConfigPath + "/" + filePath;
        if(fileInfo.isFile){
            // 创建目标文件所在的目录
            QDir targetDir(QFileInfo(targetFilePath).absolutePath());
            if (!targetDir.exists()) {
                targetDir.mkpath(".");
            }
            QFile file(targetFilePath);
            if (file.open(QIODevice::WriteOnly)) {
                file.write(zipreader.fileData(filePath.toUtf8()));
                file.close();
            } else {
                qDebug() << "Failed to open file for writing:" << targetFilePath;
                return false;
            }
        }
    }
    return true;
}

bool TcpBusiness3568::removeDirectory(const QString &dirPath)
{
    QDir dir(dirPath);

    // 检查目录是否存在
    if (!dir.exists()) {
        qDebug() << "Directory does not exist:" << dirPath;
        return false;
    }

    // 获取目录中的所有文件和子目录
    QStringList entries = dir.entryList(QDir::AllEntries | QDir::NoDotAndDotDot | QDir::Hidden);

    // 遍历并删除所有文件和子目录
    for (const QString& entry : entries) {
        QString fullPath = dir.filePath(entry);

        if (QDir(fullPath).exists()) {
            // 如果是目录，递归删除
            if (!removeDirectory(fullPath)) {
                return false; // 如果递归删除失败，返回 false
            }
        } else {
            // 如果是文件，直接删除
            if (!QFile::remove(fullPath)) {
                qDebug() << "Failed to remove file:" << fullPath;
                return false; // 如果删除文件失败，返回 false
            }
        }
    }

    return true;
}

void TcpBusiness3568::online()
{
    onlineFlag = true;
    lastHeartTime = QDateTime::currentMSecsSinceEpoch();
    //qDebug() << "[INFO] online" << lastHeartTime;
}

void TcpBusiness3568::offline()
{
    onlineFlag = false;
    lastHeartTime = 0;
    emit stopHeartTimer();
}

const bool &TcpBusiness3568::getConnectFlag()
{
    return onlineFlag;
}

void TcpBusiness3568::setRinngFlag(const bool &flag)
{
    runningFlag = flag;
}

const bool &TcpBusiness3568::getRunningFlag()
{
    return runningFlag;
}

void TcpBusiness3568::downloadByHttp(const QString &path, TcpFuncWrite func)
{
    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QString url = "http://";
        url.append(TcpClientInfos.at(0).remoteAddress);
        url.append(":8081/upload/e2000/");
        // 后期可修改为分片发送
        http.uplaodDataPost(url, path, func);
        qDebug() << "[INFO] HTTP POST request sent successfully to: " << url;
    }
}

void TcpBusiness3568::DownloadProjectInfo_Flash()
{
    // ProjectAndFileManage::instance().getCurrentProjectPath();

    QString zipFilePath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\projectzip.dat";
    compressFolder(ProjectAndFileManage::instance().getCurrentProjectPath(),
                   zipFilePath);
    QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open file: " << keyPath;
        return;
    }
    QByteArray key = keyFile.readAll();

    QByteArray encryptedArray;
    encryption(zipFilePath, encryptedArray, key);
    QString zipfileName = "projectzip.dat";

    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QString url = "http://";
        url.append(TcpClientInfos.at(0).remoteAddress);
        url.append(":8081/upload/e2000/");
        // 后期可修改为分片发送
        http.uplaodDataPost(url, encryptedArray, zipfileName, httpReplyFunc::Download2Flash);
        qDebug() << "[INFO] HTTP POST request sent successfully to: " << url;
    }
}

void TcpBusiness3568::DownloadProjectInfo_Ram()
{
    // ProjectAndFileManage::instance().getCurrentProjectPath();

    QString zipFilePath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\projectzip.dat";
    compressFolder(ProjectAndFileManage::instance().getCurrentProjectPath(),
                   zipFilePath);
    QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open file: " << keyPath;
        return;
    }
    QByteArray key = keyFile.readAll();

    QByteArray encryptedArray;
    encryption(zipFilePath, encryptedArray, key);
    QString zipfileName = "projectzip.dat";

    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QString url = "http://";
        url.append(TcpClientInfos.at(0).remoteAddress);
        url.append(":8081/upload/e2000/");
        // 后期可修改为分片发送
        http.uplaodDataPost(url, encryptedArray, zipfileName, httpReplyFunc::Download2Ram);
        qDebug() << "[INFO] HTTP POST request sent successfully to: " << url;
    }
}

void TcpBusiness3568::DownloadFileListProjectInfo_Ram()
{
    QString projectPath = ProjectAndFileManage::instance().getCurrentProjectPath();
    QString deviceName = ProjectAndFileManage::instance().getCurrentDeviceName();
    filePaths.clear();
    filePaths.push_back(projectPath + "/TASK/code_diy/task/file_list.csv");
    filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data.csv");
    filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_i.csv");
    filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_o.csv");
    filePaths.push_back(projectPath + "/TASK/all_data_flatten.csv");
    filePaths.push_back(projectPath + "/" + deviceName + "/cyclictask.csv");

    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        downloadFileList(httpReplyFunc::UploadFileList_RAM);
    }
}

void TcpBusiness3568::DownloadFileListProjectInfo_Flash_ftp()
{
    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QString projectPath = ProjectAndFileManage::instance().getCurrentProjectPath();
        QString deviceName = ProjectAndFileManage::instance().getCurrentDeviceName();
        filePaths.clear();
        filePaths.push_back(projectPath + "/TASK/code_diy/task/file_list.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_i.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_o.csv");
        filePaths.push_back(projectPath + "/TASK/all_data_flatten.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/rte2modbus.csv");
        filePaths.push_back(projectPath + "/" + deviceName + "/cyclictask.csv");
        filePaths.push_back(projectPath + "/" + deviceName + "/oneos.bin");

        fileCount = filePaths.size();

        for(int i = 0; i < filePaths.size(); i++){
            QFile localFile(filePaths[i]);
            if (!localFile.open(QIODevice::ReadOnly)) {
                qDebug() << "can not open:" << filePaths[i];
                fileCount --;
                continue;
            }
            QNetworkAccessManager *manager = new QNetworkAccessManager(this);


            QUrl ftpUrl;
            ftpUrl.setScheme("ftp");
            ftpUrl.setHost(TcpClientInfos.at(0).remoteAddress);
            ftpUrl.setUserName("tgic");
            ftpUrl.setPassword("tgic");
            QStringList strs = filePaths[i].split("/");
            QString fileName = strs[strs.size()-1];
            ftpUrl.setPath("/rte/" + fileName);

            QByteArray bytes = localFile.readAll();
            if(fileName == "oneos.bin"){
                fileSize = bytes.size();
                md5 = QCryptographicHash::hash(bytes, QCryptographicHash::Md5);
                qDebug() << "[INFO] md5" << md5.toHex(' ') << md5.size();
            }
            localFile.close();
            QNetworkRequest request(ftpUrl);
            QNetworkReply *reply = manager->put(request, bytes);

            connect(reply, &QNetworkReply::finished, [=]() {
                if (reply->error() == QNetworkReply::NoError) {
                    qDebug() << "upload succeed" << fileCount << filePaths[i].split("/").last();
                    if(--fileCount == 0){
                        Tasks::Download2Flash(fileSize);
                        //
                        Tasks::sendBoardcast(TcpClientInfos.at(0).remoteAddress, RTE_RECV_CONFIG_UPDATE_PORT, md5);
                        Tasks::sendBoardcast(TcpClientInfos.at(0).remoteAddress, RT3_RECV_CONFIG_UPDATE_PORT, md5);

                    }
                } else {
                    qDebug() << "erro:" << reply->errorString();
                }
                reply->deleteLater();
                manager->deleteLater();
            });
        }
    }
}

void TcpBusiness3568::DownloadFileListProjectInfo_Ram_ftp()
{
    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QString projectPath = ProjectAndFileManage::instance().getCurrentProjectPath();
        QString deviceName = ProjectAndFileManage::instance().getCurrentDeviceName();
        filePaths.clear();
        filePaths.push_back(projectPath + "/TASK/code_diy/task/file_list.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_i.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_o.csv");
        filePaths.push_back(projectPath + "/TASK/code_diy/task/rte2modbus.csv");
        filePaths.push_back(projectPath + "/TASK/all_data_flatten.csv");
        filePaths.push_back(projectPath + "/" + deviceName + "/cyclictask.csv");
        filePaths.push_back(projectPath + "/" + deviceName + "/oneos.bin");
        fileCount = filePaths.size();

        for(int i = 0; i < filePaths.size(); i++){
            QFile localFile(filePaths[i]);
            if (!localFile.open(QIODevice::ReadOnly)) {
                qDebug() << "can not open:" << filePaths[i];
                fileCount --;
                continue;
            }
            QNetworkAccessManager *manager = new QNetworkAccessManager(this);


            QUrl ftpUrl;
            ftpUrl.setScheme("ftp");
            ftpUrl.setHost(TcpClientInfos.at(0).remoteAddress);
            ftpUrl.setUserName("tgic");
            ftpUrl.setPassword("tgic");
            QStringList strs = filePaths[i].split("/");
            QString fileName = strs[strs.size()-1];
            ftpUrl.setPath("/rte/" + fileName);

            QByteArray bytes = localFile.readAll();

            if(fileName == "oneos.bin"){
                fileSize = bytes.size();
                md5 = QCryptographicHash::hash(bytes, QCryptographicHash::Md5);
                qDebug() << "[INFO] md5" << md5.toHex(' ') << md5.size();
            }
            localFile.close();
            QNetworkRequest request(ftpUrl);
            QNetworkReply *reply = manager->put(request, bytes);

            connect(reply, &QNetworkReply::finished, [=]() {
                if (reply->error() == QNetworkReply::NoError) {
                    qDebug() << "upload succeed" << fileCount << filePaths[i].split("/").last();
                    if(--fileCount == 0){
                        Tasks::Download2Ram(fileSize);
                        //
                        Tasks::sendBoardcast(TcpClientInfos.at(0).remoteAddress, RTE_RECV_CONFIG_UPDATE_PORT, md5);
                        Tasks::sendBoardcast(TcpClientInfos.at(0).remoteAddress, RT3_RECV_CONFIG_UPDATE_PORT, md5);
                    }
                } else {
                    qDebug() << "erro:" << reply->errorString();
                }
                reply->deleteLater();
                manager->deleteLater();
            });
        }
    }
}

void TcpBusiness3568::DownloadProjectInfo()
{
    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }else{
        emit CommServ::instance().buttonStatus(12, 3, "not connect");
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QString zipFilePath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\projectzip.dat";
        compressFolder(ProjectAndFileManage::instance().getCurrentProjectPath(),
                       zipFilePath);
        QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
        QFile keyFile(keyPath);
        if (!keyFile.open(QIODevice::ReadOnly)) {
            qDebug() << "Failed to open file: " << keyPath;
            return;
        }
        QByteArray key = keyFile.readAll();

        QByteArray encryptedArray;
        encryption(zipFilePath, encryptedArray, key);
        QString zipfileName = "projectzip.dat";
        QUrl ftpUrl;
        ftpUrl.setScheme("ftp");
        ftpUrl.setHost(TcpClientInfos.at(0).remoteAddress);
        ftpUrl.setUserName("tgic");
        ftpUrl.setPassword("tgic");
        ftpUrl.setPath("/data/" + zipfileName);
        QNetworkAccessManager *manager = new QNetworkAccessManager(this);
        QNetworkRequest request(ftpUrl);
        QNetworkReply *reply = manager->put(request, encryptedArray);

        connect(reply, &QNetworkReply::finished, [=]() {
            if (reply->error() == QNetworkReply::NoError) {
                qDebug() << "upload succed";
                emit CommServ::instance().buttonStatus(12, 1, "");
            } else {
                qDebug() << "erro:" << reply->errorString();
                emit CommServ::instance().buttonStatus(12, 3, "erro:" + reply->errorString());
            }
            reply->deleteLater();
            manager->deleteLater();
        });
    }else{
        emit CommServ::instance().buttonStatus(12, 3, "not connect");
    }
}

void TcpBusiness3568::DownloadFileListProjectInfo_Flash()
{
    QString projectPath = ProjectAndFileManage::instance().getCurrentProjectPath();
    QString deviceName = ProjectAndFileManage::instance().getCurrentDeviceName();
    filePaths.clear();
    filePaths.push_back(projectPath + "/TASK/code_diy/task/file_list.csv");
    filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data.csv");
    filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_i.csv");
    filePaths.push_back(projectPath + "/TASK/code_diy/task/group_data_o.csv");
    filePaths.push_back(projectPath + "/TASK/all_data_flatten.csv");
    filePaths.push_back(projectPath + "/" + deviceName + "/cyclictask.csv");

    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        downloadFileList(httpReplyFunc::UploadFileList_Flash);
    }
}

void TcpBusiness3568::UploadProjectInfo_ExeAndConfig()
{
    // 老代码
    // if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
    //     TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    // }
    // if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
    //     QString url = "http://";
    //     url.append(TcpClientInfos.at(0).remoteAddress);
    //     url.append(":8081/download/e2000/projectzip.dat");
    //     // 后期可修改为分片发送
    //     http.sendGetRequest(url, httpReplyFunc::UploadExeAndConfig);
    //     qDebug() << "[INFO] HTTP POST request sent successfully to: " << url;
    // }

    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QNetworkAccessManager *manager = new QNetworkAccessManager(this);

        // 设置 FTP URL (包含认证信息)
        QUrl ftpUrl;
        ftpUrl.setScheme("ftp");
        ftpUrl.setHost(TcpClientInfos.at(0).remoteAddress);
        ftpUrl.setUserName("tgic");
        ftpUrl.setPassword("tgic");
        ftpUrl.setPath("/data/projectzip.dat");

        QNetworkRequest request(ftpUrl);
        QNetworkReply *reply = manager->get(request);

        // 连接信号
        connect(reply, &QNetworkReply::finished, [=]() {
            if (reply->error() == QNetworkReply::NoError) {
                QByteArray data = reply->readAll();
                if(!uploadProject(data, true)){
                    emit CommServ::instance().buttonStatus(10, 2, "uploadExe fail");
                }
                QDir dir(TcpBusiness3568::instance().uploadExeAndConfigPath);
                QString folderName = dir.dirName(); // 直接获取目录名称
                QString fileName = folderName + ".proj";
                QString filePath = dir.filePath(fileName);
                // 发送完成信号
                emit CommServ::instance().buttonStatus(10, 1, fileName);
            } else {
                qDebug() << "Error:" << reply->errorString();
                emit CommServ::instance().buttonStatus(10, 2, "Error: " + reply->errorString());
            }
            reply->deleteLater();
            manager->deleteLater();
        });
    }
}

void TcpBusiness3568::UploadDetailBin()
{
    if(networksMsg.size() < 2)return;
    qDebug() << "enter UploadDetailBin";
    QNetworkAccessManager *manager = new QNetworkAccessManager(this);
    QUrl ftpUrl;
    ftpUrl.setScheme("ftp");
    ftpUrl.setHost(networksMsg.at(1).IP);
    ftpUrl.setUserName("tgic");
    ftpUrl.setPassword("tgic");
    ftpUrl.setPath("/sub_i_init.cfg");

    QNetworkRequest request(ftpUrl);
    qDebug() << "[INFO] ftpUrl" << ftpUrl.toString();
    QNetworkReply *reply = manager->get(request);

    // 连接信号
    connect(reply, &QNetworkReply::finished, [=]() {
        if (reply->error() == QNetworkReply::NoError) {
            QByteArray data = reply->readAll();
            DeviceAndNetworkManage::instance().readDetail(data);
            qDebug() << "readDetail success" << reply->url();
            CommServ::instance().getConfigDir();
            QFile detail(DeviceAndNetworkManage::instance().getConfigDir() + "/detail.bin");
            if(!detail.open(QIODevice::WriteOnly)){
                qDebug() << "write fail" << DeviceAndNetworkManage::instance().getConfigDir();
            }
            detail.write(data);
            detail.close();
            // 发送完成信号
            emit CommServ::instance().buttonStatus(13, 1, "");
        } else {
            qDebug() << "UploadDetailBin Error:" << reply->errorString();
            emit CommServ::instance().buttonStatus(13, 2, "Error: " + reply->errorString());
        }
        reply->deleteLater();
        manager->deleteLater();
    });
}

void TcpBusiness3568::DownloadConfigBin(const QByteArray& data)
{
    if(networksMsg.size() < 2)return;
    QNetworkAccessManager *manager = new QNetworkAccessManager(this);

    // 设置 FTP URL (包含认证信息)
    QUrl ftpUrl;
    ftpUrl.setScheme("ftp");
    ftpUrl.setHost(networksMsg.at(1).IP);
    ftpUrl.setUserName("tgic");
    ftpUrl.setPassword("tgic");
    ftpUrl.setPath("/config.bin");
    QNetworkRequest request(ftpUrl);
    QNetworkReply *reply = manager->put(request, data);
    QByteArray m_md5 = QCryptographicHash::hash(data, QCryptographicHash::Md5);
    connect(reply, &QNetworkReply::finished, [manager, reply, m_md5]() {
        if (reply->error() == QNetworkReply::NoError) {
            qDebug() << "upload succed" << m_md5.toHex(' ');
            CommServ::sendDownloadConfig(m_md5);
            emit CommServ::instance().buttonStatus(14, 1, "");
        } else {
            qDebug() << "erro:" << reply->errorString() << m_md5.toHex(' ');
            emit CommServ::instance().buttonStatus(14, 3, "erro:" + reply->errorString());
        }
        reply->deleteLater();
        manager->deleteLater();
    });
}

void TcpBusiness3568::UploadProjectInfo_Exe()
{
    // 老代码
    // QString url = "http://";
    // url.append("*************");
    // url.append(":8081/download/e2000/projectzip.dat");
    // // 后期可修改为分片发送
    // http.sendGetRequest(url, httpReplyFunc::UploadExe);
    // qDebug() << "[INFO] HTTP POST request sent successfully to: " << url;
    // if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
    //     TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    // }
    // if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
    //     QString url = "http://";
    //     url.append(TcpClientInfos.at(0).remoteAddress);
    //     url.append(":8081/download/e2000/projectzip.dat");
    //     // 后期可修改为分片发送
    //     http.sendGetRequest(url, httpReplyFunc::UploadExe);
    //     qDebug() << "[INFO] HTTP POST request sent successfully to: " << url;
    // }


    if(networksMsg.size() > 1 && TcpClientInfos.size() > 0){
        TcpClientInfos[0].remoteAddress = networksMsg.at(1).IP;
    }
    if(TcpClientInfos.size() > 0 && TcpClientInfos.at(0).remoteAddress.size() > 0){
        QNetworkAccessManager *manager = new QNetworkAccessManager(this);

        // 设置 FTP URL (包含认证信息)
        QUrl ftpUrl;
        ftpUrl.setScheme("ftp");
        ftpUrl.setHost(TcpClientInfos.at(0).remoteAddress);
        ftpUrl.setUserName("tgic");
        ftpUrl.setPassword("tgic");
        ftpUrl.setPath("/data/projectzip.dat");

        QNetworkRequest request(ftpUrl);
        QNetworkReply *reply = manager->get(request);

        // 连接信号
        connect(reply, &QNetworkReply::finished, [=]() {
            if (reply->error() == QNetworkReply::NoError) {
                QByteArray data = reply->readAll();
                if(!uploadProject(data, false)){
                    emit CommServ::instance().buttonStatus(10, 2, "uploadExe fail");
                }
                QDir dir(TcpBusiness3568::instance().uploadExeAndConfigPath);
                QString folderName = dir.dirName(); // 直接获取目录名称
                QString fileName = folderName + ".proj";
                QString filePath = dir.filePath(fileName);
                // 发送完成信号
                emit CommServ::instance().buttonStatus(10, 1, fileName);
            } else {
                qDebug() << "Error:" << reply->errorString();
                emit CommServ::instance().buttonStatus(10, 2, "Error: " + reply->errorString());
            }
            reply->deleteLater();
            manager->deleteLater();
        });
    }
}

void TcpBusiness3568::testhttp()
{
    // 发送 GET 请求到百度
    QUrl getUrl("https://www.baidu.com");
    http.sendGetRequest(getUrl);

    // 发送 POST 请求到百度
    QUrl postUrl("https://www.baidu.com/s");
    QByteArray postData = "wd=Qt&pn=0";
    http.sendPostRequest(postUrl, postData);
}

void TcpBusiness3568::heartBeat()
{
    if((!onlineFlag) ||
        ((QDateTime::currentMSecsSinceEpoch() - lastHeartTime) > 8000)){
        qDebug() << "[INFO] heartBeat" << QDateTime::currentMSecsSinceEpoch() << lastHeartTime;
        TcpService::instance().stop3568();// 断开连接
    }else{
        Tasks::heartBeatTcp();
    }
}
