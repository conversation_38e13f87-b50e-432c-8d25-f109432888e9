﻿#ifndef LD_OBJECT_H
#define LD_OBJECT_H

#include <QObject>
#include <QString>
#include <QDomDocument>
#include <QDomElement>
#include <QDomNode>
#include <QJsonObject>
#include <QJsonArray>
#include <QSharedPointer>
#include <QList>
#include <QMap>
#include <QVector>
#include <QFile>
#include <QTextStream>

class LDNetwork;

//引脚连接
//一个输出端可以连接多个输入端
class LDConnection : public QObject
{
    Q_OBJECT
public:
    LDConnection() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
    QString getKey();
signals:

public:
    //实例名UUID
    QString InstanceName;
    //连接输出端的数据类型
    QString SourceDataType;
    //输出端元件号
    int SourceComponentNumber;
    //输出端引脚IdnewConnection
    int SourcePinId;
    //连接输入端的数据类型
    QString TargetDataType;
    //输入端元件号
    int TargetComponentNumber;
    //输入端引脚Id
    int TargetPinId;
    // 备用属性(暂时无用)
    QString PlotPath;
    // 备用属性(暂时无用)
    int Length;
    //是否显示
    bool Visible = true;
    //该连接绑定变量名称
    QString VarName;
    QString VarOwned;
    QString TaskName;
    int TaskOrderNumber = 9999;
    // 输出端链接序号 从0开始
    int SourceConnectIndex = -1;
    // 输入端链接序号 从0开始
    int TargetConnectIndex = -1;
};

//所有连接
class LDConnections : public QObject
{
    Q_OBJECT
public:
    LDConnections() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonArray toJsonArray();
signals:

public:
    QList<QSharedPointer<LDConnection>> connectionList;
};

// 一般情况下块只有一个OUT引脚，一个EN和一个ENO引脚是逻辑控制引脚，可能有多个IN引脚
// 除非是由用户导入的块，则可能存在多个OUT引脚
class LDConnector : public QObject
{
    Q_OBJECT
public:
    LDConnector() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //实例名UUID唯一
    QString InstanceName;
    //引脚Id规则：EN=1 ENO=-1 输入引脚从2开始递增，输出引脚从-2开始递减
    int PinId;
    //备用属性(暂时无用 默认false)
    bool NewAdd;
    //引脚名称: EN ENO，如果是输入引脚，则需要添加IN1(IN+序号)，如果是输出引脚，则需要添加OUT1(OUT+序号)
    QString Name;
    QString DataType; //如果所属元件为变量元件则DataType是Variable, 其它元件类型DataType为Bool
    bool SupportChangeDataType = false; // 备用属性(暂时无用)
    // 备用属性(暂时无用)
    QString DataTypeGroup;
    //取反标志位(默认false)
    bool Negated = false;
    //输入输出 Input Output EN ENO
    QString Direction;
    // 备用属性(暂时无用)
    bool FastSignal = false;
    // 备用属性(暂时无用)
    bool isInitVar = false;
    // 备用属性(暂时无用)
    QString InitValue = QString();
    //注释
    QString Comment;
    //引脚在当前页面上的横轴位置，以网格作为单位
    int XPos;
    //引脚在当前页面上的纵轴位置，以网格作为单位
    int YPos;
    //是否显示引脚, 变量元件引脚不显示
    bool Visible = true;
    //是否是逻辑控制引脚，默认false, EN和ENO是true
    bool IsLogical = false;
    //对应的子变量元件Numnber, -1表示没有, 如何链接了变量元件，则记录变量元件的Numnber
    int ChildNumber = -1;
};

// 元件引脚规则：除变量元件外每个元件必须得有EN和ENO引脚
// Type:Variable + ChildType:Constant +AuxContent:1 +AuxContent1:Start = 启动常量元件
// Type:Variable + ChildType:Local + AuxContent:??? = Variable元件 
// Type:Block + ChildType:Func + AuxContent:Conatct = Conatct触点元件
// Type:Block + ChildType:Func + AuxContent:Or = OR元件
// Type:Block + ChildType:Func + AuxContent:Coil = Coil元件 引脚：EN + ENO + Variable引脚
// Type:Block + ChildType:Func + AuxContent:Set0 = Set0元件 引脚：EN + ENO + Variable引脚
// Type:Block + ChildType:Func + AuxContent:Set1 = Set1元件 引脚：EN + ENO + Variable引脚
// Type:Block + ChildType:Func + AuxContent:Jump = Jump元件 引脚：EN + ENO
// Type:Block + ChildType:Func + AuxContent:Return = Return元件 引脚：EN + ENO
// Type:Block + ChildType:Func/FB + AuxContent:用户自定义块 = 用户自定义块 
// 用户自定义块信息由前端传入,但是用户自定义块没有EN和ENO引脚,需要自己添加，参考FBD添加块元件代码
// 复位R Set0  // 返回Ret return // 置位S Set1 // 线圈转换为Set0 和 Set1
class LDComponent : public QObject
{
    Q_OBJECT
public:
    LDComponent() {};
    //排序引脚记录
    void sortPinList();
    //计算块内引脚的位置偏移量
    void setPinPosOffset();
    //根据引脚类型更新数据类型选择
    void updateDataTypeLocal();

    //根据引脚ID查找引脚
    QSharedPointer<LDConnector> searchConnectorFromPinId(int pinId);

    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //元件名称，可修改 Type_strUUID
    QString Name;
    //实例名UUID唯一 strUUID
    QString InstanceName;
    //所属网路序号
    int NetworkNumber;
    //是否启用，承接所属网络的启用
    bool Enable;
    //自增id，单文件唯一
    int Number;
    //绑定的任务
    QString TaskName;
    //任务执行顺序
    int TaskOrderNumber;
    //元件类型 Variable变量元件  Block块元件  Jump跳转元件  Return返回元件  Text注释元件
    QString Type;
    //变量元件如下: Global全局 Constant常量 Local本地 Static静态 Input输入 Output输出 InOut输入输出 Expression表达式 Address拓扑地址
    //块元件如下: FUNC函数 FB功能块
    //子类型
    QString ChildType; //作用域
    //辅助文档 变量元件下是变量名称 FUNC下是函数名称，FB下是功能块名称,跳转元件下是跳转标签名称
    QString AuxContent;
    //辅助文档 变量元件下是变量名称所属Owned
    QString AuxContent1;
    //可变类型选中内容
    QString DataType_Local;
    //代码源
    QString Source;
    //输入引脚数量
    int InputPinNum;
    //输出引脚数量
    int OutputPinNum;
    //注释
    QString Comment;
    //支持的输入引脚类型
    QString SupportsInPinType;
    //支持的输出引脚类型
    QString SupportsOutPinType;
    // 备用属性(暂时无用)
    bool SupportsInPinAdd;
    // 备用属性(暂时无用)
    bool SupportsOutPinAdd;
    //元件左上角所在单元格X坐标 （按坐标计算）
    int XPos;
    //元件左上角所在单元格Y坐标 （按坐标计算）
    int YPos;
    //元件的宽度 固定3
    int Width;
    //元件的高度:元件基础高度3 + EN引脚 + 输出引脚数量
    int Height = 3;
    //元件的最小宽度
    int MinWidth = 3;
    //元件是否显示 备用属性(暂时无用) 默认false
    bool Visible = true;
    //变量元件在左边=0 中间=1 右边=2 中间为预留暂时不管，其它元件为3
    int LeftOrRight = 1;
    //父元件Number
    int ParentNumber = 0;
    //与父元件连接的引脚编号
    int ParentPinId = 0;
    //与父元件连接的引脚数据类型
    QString ParentPinDataType;

    //所有引脚
    QList<QSharedPointer<LDConnector>> connectorList;

    QSharedPointer<LDNetwork> network;
    //分支号
    int BranchId = 0;
};

//所有元件
class LDComponents : public QObject
{
    Q_OBJECT
public:
    LDComponents() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonArray toJsonArray();

signals:

public:
    // 单个文件中所有元件
    QMap<int, QSharedPointer<LDComponent>> componentMap;
};


//网络
class LDNetwork : public QObject
{
    Q_OBJECT
public:
    LDNetwork(QObject* parent = nullptr) {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonObject toJsonObject();
signals:

public:
    //网络类型 Network默认值，目前预留
    QString Type;
    //网络是否启用 "0"不启用=不编译   默认"1"启用
    bool Enable = true;
    //网络标号 作为逻辑跳转元件的目的地(命名规则同变量)
    QString Label;
    //注释
    QString Comment;
    //网络序号 从0开始，逻辑执行的先后顺序
    int Number;
    //网络的高度 宽度和页面宽度一致
    int Height;
public:
    QMap<int, QSharedPointer<LDComponent>> componentMap;
};

//所有网络
class LDNetworks : public QObject
{
    Q_OBJECT
public:
    LDNetworks() {};
    void toXml(QDomDocument& doc, QDomElement& pNode);
    void fromXml(QDomNode node);
    QJsonArray toJsonArray();
    //重排网络
    void sortNetworkList();

signals:

public:
    QMap<int, QSharedPointer<LDNetwork>> networkMap;
};

//通用文档主类
class LDFile : public QObject
{
    Q_OBJECT
public:
    LDFile() {};

    void toXml(QString path);
    QByteArray toXml();
    void fromXml(QString path);
    void fromXml(QByteArray bytes);

    //网络与元件对应
    void networkWithComponent();
    //获取该文件的最大元件号
    int getMaxComonpentNumber();
    //根据任务名称获取当前最大的执行
    int getMaxOrderNumbber(QString taskName);
    //清理无效链接
    void clearInvalidConnections();
    //重排所有块的执行顺序 按先从上到下 后从左到右
    QJsonArray sortAllTaskOrderNumber();
    //按实例名称找到元件
    QSharedPointer<LDComponent> searchComponentFromName(const QString name);
    QSharedPointer<LDComponent> searchComponentFromNumber(int number);
    //调整每个网络的高度
    void reSize();

    //检查块的out是否都有链接
    bool checkBlockOutConnection();
    //检查环路 返回形成环的最后一条边的实例名称
    bool checkCycle();
    bool isCyclicUtil(QMap<int, QVector<int>>& adj, int u,
        QVector<bool>& visited, QVector<bool>& recStack);


    QJsonObject toJsonObject();

signals:

public:
    //名称 与该文件名称对应（不包含扩展名）
    QString Name;
    //类型 PROGRAM（程序）FUNCTION_BLOCK（功能块)
    QString Type = "PROGRAM";
    //代码FBD CFC SFC等
    QString Code;
    //版本
    QString Version = "1.0.0";
    //宽度数量
    int WidthNumber = 100;
    //高度数量
    int HeightNumber = 100;
    //创建日期
    QString CreatedOn;
    //修改日期
    QString LastChange;
    //作者
    QString Author;
    //注释
    QString Comment;
    //绑定任务名称
    QString TasksName = "T2";

    //文件路径
    QString filePath;
    //编译路径
    QString compliePath;

    //所有的网络
    QSharedPointer<LDNetworks> networks = QSharedPointer<LDNetworks>(new LDNetworks());
    //所有的元件
    QSharedPointer<LDComponents> components = QSharedPointer<LDComponents>(new LDComponents());
    //所有的连接
    QSharedPointer<LDConnections> connections = QSharedPointer<LDConnections>(new LDConnections());
};

#endif // NEWFBDOBJECT_H