﻿/*
 * @Author: liquan
 * @Date: 2024-02-28 14:17:25
 * @Last Modified by: liquan
 * @Last Modified time: 2024-04-16 17:30:15
 */

#pragma execution_character_set("utf-8")

#include <QCryptographicHash>
#include <QSettings>
#include <QXmlStreamWriter>
#include <QRegularExpression>
#include <QPrinter>
#include <QPrintDialog>
#include <QPrintPreviewDialog>
#include <QPageSetupDialog>
#include <QMessageBox>
#include <QPainter>
#include <QTextDocument>
#include <QJsonDocument>
#include <QJsonArray>
#include <QJsonObject>
#include "projectandfilemanage.h"
#include "hlog.h"
#include "recent_project.h"
#include "sqlorm.h"
#include "projecttreetemplatemanage.h"
#include "qzip.h"
#include <QRandomGenerator>

// 枚举内容类型
enum ContentType
{
    Code,
    Image,
    Unknown
};

ProjectAndFileManage &ProjectAndFileManage::instance()
{
    static ProjectAndFileManage m_instance;
    return m_instance;
}

// URL转换为本地文件路径
void ProjectAndFileManage::handleString(QString &path)
{
    QUrl url(path);
    // 如果URL不是一个本地文件URL则不做处理
    if (!url.isLocalFile())
    {
        return;
    }
    path = url.toLocalFile();
}

void ProjectAndFileManage::modifyProjectVersion(const QVariant &value)
{
    currentProject->setValue("ProjectVersion", value);
}

void ProjectAndFileManage::modifyProjectAuthor(const QVariant &value)
{
    currentProject->setValue("ProjectAuthor", value);
}

void ProjectAndFileManage::modifyProjectDescription(const QVariant &value)
{
    currentProject->setValue("ProjectDescription", value);
}

// 初始化
void ProjectAndFileManage::init(QSqlDatabase &db)
{
    m_db = db;
}

// 初始化数据库表
void ProjectAndFileManage::databaseTableInit()
{
    QSqlError daoError = qx::dao::create_table<ProjectTree>(&m_db);
    LOG_ERROR_DEFAULT("databaseTableInit");
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("create table ProjectTree :" + daoError.text().toStdString());
    }
    QSqlError daoError1 = qx::dao::create_table<FileList>(&m_db);
    if (daoError1.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("create table FileList :" + daoError1.text().toStdString());
    }
}

// 从文件读取加载到currentProject
// param: file:///D:/ee/ee.proj
bool ProjectAndFileManage::readFile(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QFile::ReadOnly))
    {
        LOG_ERROR_DEFAULT(std::string("open error") + file.errorString().toUtf8().constData());
        return false;
    }
    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    QString str = stream.readAll();
    file.close();

    QJsonParseError jsonError;
    QJsonDocument doc = QJsonDocument::fromJson(str.toUtf8(), &jsonError);
    // qInfo() << __FILE__ << __func__ << __LINE__ << "doc : " << doc;
    if (jsonError.error != QJsonParseError::NoError || doc.isNull())
    {
        return false;
    }
    if (currentProject.isNull())
    {
        currentProject = QSharedPointer<Project>::create();
    }
    currentProject->fromJson(doc.object());
    // qInfo() << "readFile currentProject : " << currentProject->toJson();
    return true;
}

// 从currentPorject读取转换为json的保存至文件
bool ProjectAndFileManage::saveFile(const QString &filePath)
{
    //
    if (currentProject.isNull())
    {
        LOG_ERROR_DEFAULT("currentProject is null");
        return false;
    }
    QFile file(filePath);
    if (!file.open(QFile::WriteOnly))
    {
        LOG_ERROR_DEFAULT("open error " + filePath.toStdString());
        return false;
    }
    // qInfo() << __FILE__ << __func__ << __LINE__ << "currentProject->toJson() : " << currentProject->toJson();
    QJsonDocument doc(currentProject->toJson());
    QTextStream stream(&file);
    stream.setCodec("UTF-8");
    stream << doc.toJson();
    // qInfo() << __FILE__ << __func__ << __LINE__ << "doc.toJson() : " << QString::fromUtf8(doc.toJson());
    file.close();

    return true;
}

// 是否已经打开了项目
bool ProjectAndFileManage::isProjectOpened()
{
    return !currentProject.isNull();
}

// 打开项目
// param1: file:///D:/ee/ee.proj
// 先判断是否有当前工程，先关闭再打开,打开之前是否进行基本完整性检查(检查数据库 文件管理数据结构的表 中的文件是否与项目目录中一致)
QSqlDatabase &ProjectAndFileManage::openProject(const QString &projectPath)
{
    closeProject();
    currentProjectName = projectPath;
    handleString(currentProjectName);
    // LOG_TRACE_DEFAULT(("currentProjectName:" + currentProjectName).toStdString());
    QFileInfo fileInfo(currentProjectName);
    QString fileName = fileInfo.fileName();
    // qInfo() << "projectName : " << projectName;
    if (!isProjectNameValid(fileName))
    {
        LOG_ERROR_DEFAULT("project name is invalid");
    }
    // 读取项目文件内容
    if (!ProjectAndFileManage::readFile(currentProjectName))
    {
        LOG_ERROR_DEFAULT("readFile error");
        return m_db;
    }
    // 获取项目路径
    currentProjectPath = fileInfo.absolutePath();

    // 连接数据库
    QString destConfigFile = currentProjectPath + QDir::separator() + "data" + QDir::separator() + "manage.db";

    if (!SqlOrm::instance().databaseConnect(destConfigFile, "Password123."))
    {
        LOG_ERROR_DEFAULT("connect sql error");
        return m_db;
    }
    QSqlDatabase db = SqlOrm::instance().getDatabase(destConfigFile);

    init(db);

    // 添加进最近打开项目
    RecentProject::instance().appendRecentProject(currentProject->getIcon(), currentProject->getName(), currentProjectName);
    RecentProject::instance().refreshRecentProject(currentProjectName);

    // 每次打开项目时重新获取
    getHardwareConfigTemplate();

    return m_db;
}

// 判断项目名是否合法
bool ProjectAndFileManage::isProjectNameValid(const QString &projectName)
{
    // 不可包含非法字符 \ / : * ? " < > |
    QRegExp regExp("[\\\\/:*?\"<>|]");
    return !projectName.contains(regExp);
}

// 判断项目版本是否合法
bool ProjectAndFileManage::isProjectVersionValid(const QString &projectVersion)
{
    // 版本号格式为x.x.x
    QRegExp regExp("^[0-9]+\\.[0-9]+\\.[0-9]+$");
    return regExp.exactMatch(projectVersion);
}

// param1: ee
// param2: file:///D:/
//  创建新项目时，先判断是否有当前工程，如果有先关闭再创建，创建完成后打开
QSqlDatabase &ProjectAndFileManage::createProject(const QString &projectName, const QString &projectPath,
                                                  const QString &projectVersion, const QString &projectAuthor,
                                                  const QString &projectDescription, const QString &projectIcon)
{

    closeProject();
    m_db = QSqlDatabase();
    if (!isProjectNameValid(projectName))
    {
        LOG_ERROR_DEFAULT("project name is invalid");
        return m_db;
    }
    /* if (!isProjectVersionValid(projectVersion))
    {
        if (!projectVersion.isEmpty())
        {
            LOG_ERROR_DEFAULT("project version is invalid");
            return QSqlDatabase();
        }
    } */
    // projectPath = projectPath + QDir::separator() + projectName;
    QString projectFullPath = projectPath + QDir::separator() + projectName;
    handleString(projectFullPath);
    currentProjectName = projectFullPath + QDir::separator() + projectName + ".proj";
    currentProjectPath = projectFullPath;
    // qInfo() << "projectPath : " << projectPath;
    QString projectPathTemp = projectFullPath;
    handleString(projectPathTemp);
    // 先判断该目录是否存在，不存在则创建项目目录
    QDir dir(projectPathTemp);
    if (dir.exists())
    {
        LOG_ERROR_DEFAULT("project path already exists");
        return m_db;
    }
    // qInfo() << "projectFullPath : " << projectFullPath;
    // 创建工程目录和data子目录
    if (!dir.mkpath(projectPathTemp + QDir::separator() + "data"))
    {
        LOG_ERROR_DEFAULT("mkpath error");
        return m_db;
    }

    // 拷贝根目录下模板数据库template.db到在data目录下
    QString configFile = QDir::currentPath() + QDir::separator() + "Settings" + QDir::separator() + "template.db";
    QString destConfigFile = projectPathTemp + QDir::separator() + "data" + QDir::separator() + "manage.db";

    // qInfo() << "configFile : " << configFile;
    // qInfo() << "destConfigFile : " << destConfigFile;

    if (!QFile::copy(configFile, destConfigFile))
    {
        LOG_ERROR_DEFAULT("copy error");
    }

    // 判断当前目录下是否有同名项目
    if (QFile::exists(currentProjectName))
    {
        LOG_ERROR_DEFAULT("project already exists");
        return m_db;
    }
    // qInfo() << "currentProjectName : " << currentProjectName;
    QFile file(currentProjectName);
    if (!file.open(QFile::WriteOnly))
    {
        LOG_ERROR_DEFAULT("open error");
        return m_db;
    }
    file.close();

    if (currentProject.isNull())
    {
        currentProject = QSharedPointer<Project>::create();
    }
    currentProject->setValue("ProjectName", projectName);
    // qInfo() << "projectFullPath : " << projectFullPath;
    projectFullPath = projectFullPath.replace("\\", "/");
    projectFullPath = projectFullPath.replace("\\\\", "/");
    currentProject->setValue("ProjectPath", projectFullPath);
    currentProject->setValue("ProjectVersion", projectVersion);
    currentProject->setValue("ProjectAuthor", projectAuthor);
    currentProject->setValue("ProjectDescription", projectDescription);
    currentProject->setValue("Protection", ""); // 默认为空 需要手动保护
    currentProject->setValue("Icon", projectIcon);
    currentProject->setValue("CreateCompute", QSysInfo::machineHostName());
    currentProject->setValue("CreateTime", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    currentProject->setValue("LastModifyTime", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));

    currentProjectName.replace("\\", "/");
    currentProjectName.replace("\\\\", "/");
    qInfo() << __func__ << __LINE__ << "currentProjectName : " << currentProjectName;
    saveFile(currentProjectName);
    // 初始化当前项目数据库
    SqlOrm::instance().databaseConnect(destConfigFile, "Password123.");
    QSqlDatabase db = SqlOrm::instance().getDatabase(destConfigFile);
    init(db);
    // 在数据库中插入项目根节点
    copyProjectNode(projectName);
    // 添加进最近打开项目
    RecentProject::instance().appendRecentProject(projectIcon, projectName, currentProjectName);

    // 每次创建项目时重新获取
    getHardwareConfigTemplate();
    return m_db;
}

// 关闭项目
void ProjectAndFileManage::closeProject()
{
    if (currentProject.isNull())
    {
        LOG_ERROR_DEFAULT("currentProject is null");
        return;
    }
    currentProject->setValue("LastModifyTime", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    // qInfo() << "currentProject->getPath() : " << currentProject->getPath();
    // qInfo() << "currentProjectPath : " << currentProjectPath;
    // qInfo() << __FILE__ << __func__ << __LINE__ << "currentProjectName : " << currentProjectName;
    saveFile(currentProjectName);

    currentProject.clear();

    // 关闭当前项目数据库
    m_db.close();
    // QSqlDatabase::removeDatabase(m_db.connectionName());
    QString pathName = currentProjectPath + QDir::separator() + "data" + QDir::separator() + "manage.db";
    SqlOrm::instance().closeDatabase(pathName);
}

// 复制源文件夹中的所有文件和子文件夹到目标文件夹并
bool ProjectAndFileManage::copyRecursively(const QString &srcFilePath, const QString &tgtFilePath)
{
    QFileInfo srcFileInfo(srcFilePath);
    if (srcFileInfo.isDir())
    {
        QDir targetDir(tgtFilePath);
        targetDir.cdUp();
        if (!targetDir.mkdir(QFileInfo(tgtFilePath).fileName()))
        {
            return false;
        }
        QDir sourceDir(srcFilePath);
        QStringList fileNames = sourceDir.entryList(QDir::Files | QDir::Dirs | QDir::NoDotAndDotDot);
        foreach (const QString &fileName, fileNames)
        {
            const QString newSrcFilePath = srcFilePath + QLatin1Char('/') + fileName;
            const QString newTgtFilePath = tgtFilePath + QLatin1Char('/') + fileName;
            if (!copyRecursively(newSrcFilePath, newTgtFilePath))
            {
                return false;
            }
        }
    }
    else
    {
        if (!QFile::copy(srcFilePath, tgtFilePath))
        {
            return false;
        }
    }
    return true;
}

//  srcProejctPath: file:///D:/ee/ee.proj
//  destProjectPath: file:///D:/
// 先对要移植的项目是否已经存在并做完整性校验，再判断要移植的路径下是否有同名项目，如果有则提示是不允许移植，如果没有则直接移植
bool ProjectAndFileManage::copyProject(const QString &srcProejctPath, const QString &destProjectName,
                                       const QString &destProjectPath, const QString &projectVersion,
                                       const QString &desProjectAuthor, const QString &destProjectDescription) // 移植项目
{

    // 关闭当前项目
    QString srcProjectPathTemp = srcProejctPath;
    handleString(srcProjectPathTemp);
    currentProjectName = srcProjectPathTemp;

    // destProjectPath = destProjectPath + QDir::separator() + destProjectName + ".proj";
    QString destProjectPathTemp = destProjectPath + QDir::separator() + destProjectName + QDir::separator() + destProjectName +
                                  ".proj";
    handleString(destProjectPathTemp);

    // 从srcProejctPathTemp中提取文件夹路径
    QFileInfo srcFileInfo(srcProjectPathTemp);
    QString srcDirPath = srcFileInfo.absolutePath();
    // qInfo() << "srcDirPath : " << srcDirPath;

    // 移植目的项目路径不能与原项目路径相同，也不能是原项目的子目录
    // qInfo() << "srcDirPath : " << srcDirPath << " destProjectPathTemp : " << destProjectPathTemp;
    if (destProjectPathTemp.contains(srcDirPath))
    {
        LOG_ERROR_DEFAULT("project path is subdirectory");
        return false;
    }
    // 判断要移植的项目内文件是否完整
    projectBasicCheck(srcProjectPathTemp);
    // 要目的路径是否已经存在该项目
    QFile file(destProjectPathTemp);
    if (file.exists())
    {
        LOG_ERROR_DEFAULT("project already exists" + destProjectPathTemp.toStdString());
        return false;
    }

    // 从destProjectPathTemp中提取文件夹路径
    QFileInfo desFileInfo(destProjectPathTemp);
    QString desDirPath = desFileInfo.absolutePath();
    // 关闭srcProejctPathTemp的数据库
    // 拷贝项目文件夹
    if (!copyRecursively(srcDirPath, desDirPath))
    {
        LOG_ERROR_DEFAULT("copyDir error");
    }

    QFile readFile(srcProjectPathTemp);
    // 修改目的项目文件夹下的manage.db文件中的项目信息
    // 连接数据库
    QString destConfigFile = desDirPath + QDir::separator() + "data" + QDir::separator() + "manage.db";
    // qInfo() << "destConfigFile : " << destConfigFile;
    SqlOrm::instance().databaseConnect(destConfigFile, "Password123.");
    QSqlDatabase db = SqlOrm::instance().getDatabase(destConfigFile);
    init(db);
    // 修改项目信息
    // qInfo() << "desFileInfo.baseName() : " << desFileInfo.baseName();
    if (currentProject.isNull())
    {
        currentProject = QSharedPointer<Project>::create();
    }
    // qInfo() << __FILE__ << __func__ << __LINE__ << "ProjectName : " << desFileInfo.baseName();
    currentProject->setValue("ProjectName", desFileInfo.baseName());
    // qInfo() << "ProjectPath : " << desDirPath;
    desDirPath = desDirPath.replace("\\", "/").replace("\\\\", "/");
    currentProject->setValue("ProjectPath", desDirPath);
    currentProject->setValue("ProjectVersion", projectVersion);
    currentProject->setValue("ProjectAuthor", desProjectAuthor);
    currentProject->setValue("ProjectDescription", destProjectDescription);
    currentProject->setValue("LastModifyTime", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    // 修改项目树信息
    // 修改根节点的nodeName和parentName
    qx_query query;
    query.where("parentId").isEqualTo(0);
    ProjectTree projectTreeNode;
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
    }
    projectTreeNode.nodeName = desFileInfo.baseName();
    projectTreeNode.parentName = desFileInfo.baseName();
    daoError = qx::dao::update(projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
    }
    // 修改所有子节点的parentName
    QList<QSharedPointer<ProjectTree>> projectTreeList;
    daoError = qx::dao::fetch_all(projectTreeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all ProjectTree :" + daoError.text().toStdString());
    }
    for (int i = 0; i < projectTreeList.size(); i++)
    {
        if (projectTreeList.at(i)->parentId == 1)
        {
            projectTreeList.at(i)->parentName = desFileInfo.baseName();
            daoError = qx::dao::update(projectTreeList.at(i), &m_db);
            if (daoError.type() != QSqlError::NoError)
            {
                LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
            }
        }
    }
    // 添加进最近打开项目
    currentFileName.replace("\\", "/");
    currentFileName.replace("\\\\", "/");
    RecentProject::instance().appendRecentProject(currentProject->getIcon(), currentProject->getName(), currentProjectName);
    // 关闭当前项目数据库
    db.close();
    QFileInfo fileInfo(srcProjectPathTemp);
    QFile::rename(desDirPath + QDir::separator() + fileInfo.fileName(), destProjectPathTemp);
    // qInfo() << __FILE__ << __func__ << __LINE__ << "destProjectPathTemp : " << destProjectPathTemp;
    saveFile(destProjectPathTemp);
    return true;
}

// 项目另存为
Q_INVOKABLE bool ProjectAndFileManage::saveProjectAs(const QString &destProjectName, const QString &destProjectPath)
{
    QString createCompute = currentProject->getCreateCompute();
    QString createTime = currentProject->getCreateTime();
    QString icon = currentProject->getIcon();
    QString projectAuthor = currentProject->getAuthor();
    QString projectDescription = currentProject->getDescription();
    QString projectVersion = currentProject->getVersion();
    QString protection = currentProject->getProtection();

    // 关闭当前项目
    closeProject();
    QString srcProjectPathTemp = getCurrentProjectPath();
    // qInfo() << "srcProjectPathTemp : " << srcProjectPathTemp;
    handleString(srcProjectPathTemp);

    // 生成新的项目路径
    QString destProjectPathTemp = destProjectPath + destProjectName + QDir::separator() + destProjectName + ".proj";
    currentProjectName = destProjectPathTemp;
    getCurrentProjectPath();
    // qInfo() << "生成新的项目路径: " << destProjectPathTemp;
    handleString(destProjectPathTemp);

    // 从srcProjectPathTemp中提取文件夹路径
    // QFileInfo srcFileInfo(srcProjectPathTemp);
    // QString srcDirPath = srcFileInfo.absolutePath();

    // 移植目的项目路径不能与原项目路径相同，也不能是原项目的子目录
    if (destProjectPathTemp.contains(srcProjectPathTemp))
    {
        // qInfo() << "destProjectPathTemp : " << destProjectPathTemp << "srcDirPath : " << srcProjectPathTemp;
        LOG_ERROR_DEFAULT("项目路径是子目录");
        return false;
    }

    // 检查目标路径下是否已有同名项目
    QFile file(destProjectPathTemp);
    if (file.exists())
    {
        LOG_ERROR_DEFAULT("目标路径中已存在同名项目: " + destProjectPathTemp.toStdString());
        return false;
    }

    // 从目标路径中提取文件夹路径
    QFileInfo desFileInfo(destProjectPathTemp);
    QString desDirPath = desFileInfo.absolutePath();

    // 递归拷贝原项目文件夹到新路径
    if (!copyRecursively(srcProjectPathTemp, desDirPath))
    {
        LOG_ERROR_DEFAULT("复制目录失败");
        return false;
    }

    // 连接新项目的数据库，并修改项目信息
    QString destConfigFile = desDirPath + QDir::separator() + "data" + QDir::separator() + "manage.db";
    SqlOrm::instance().databaseConnect(destConfigFile, "Password123.");
    QSqlDatabase db = SqlOrm::instance().getDatabase(destConfigFile);
    init(db);
    if (currentProject.isNull())
    {
        currentProject = QSharedPointer<Project>::create();
    }
    currentProject->setValue("CreateCompute", createCompute);
    currentProject->setValue("CreateTime", createTime);
    currentProject->setValue("Icon", icon);
    currentProject->setValue("LastModifyTime", QDateTime::currentDateTime().toString("yyyy-MM-dd hh:mm:ss"));
    currentProject->setValue("ProjectAuthor", projectAuthor);
    currentProject->setValue("ProjectDescription", projectDescription);
    currentProject->setValue("ProjectVersion", projectVersion);
    currentProject->setValue("ProjectName", desFileInfo.baseName());
    desDirPath = desDirPath.replace("\\", "/").replace("\\\\", "/");
    currentProject->setValue("ProjectPath", desDirPath);
    currentProject->setValue("Protection", protection);

    // qInfo() << "ProjectName: " << currentProject->getName() << "ProjectPath :" << currentProject->getPath();

    // 修改项目树根节点及其子节点的名称和父名称
    qx_query query;
    query.where("parentId").isEqualTo(0);
    ProjectTree projectTreeNode;
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("获取项目树根节点失败: " + daoError.text().toStdString());
    }
    projectTreeNode.nodeName = desFileInfo.baseName();
    projectTreeNode.parentName = desFileInfo.baseName();
    daoError = qx::dao::update(projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("更新项目树根节点失败: " + daoError.text().toStdString());
    }

    QList<QSharedPointer<ProjectTree>> projectTreeList;
    daoError = qx::dao::fetch_all(projectTreeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("获取项目树子节点失败: " + daoError.text().toStdString());
    }
    for (auto &treeNode : projectTreeList)
    {
        if (treeNode->parentId == 1)
        {
            treeNode->parentName = desFileInfo.baseName();
            daoError = qx::dao::update(treeNode, &m_db);
            if (daoError.type() != QSqlError::NoError)
            {
                LOG_ERROR_DEFAULT("更新项目树子节点失败: " + daoError.text().toStdString());
            }
        }
    }

    // 修改每个文件的路径（从unzip函数移植的逻辑）
    QList<QSharedPointer<FileList>> filelist;
    daoError = qx::dao::fetch_all(filelist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("读取文件列表出错: " + daoError.text().toStdString());
    }
    else
    {
        // 修改每个文件的路径
        for (int i = 0; i < filelist.size(); i++)
        {
            // 重新构建文件的绝对路径
            filelist.at(i)->absolutePath = desDirPath + QDir::separator() + filelist.at(i)->settingName + QDir::separator() +
                                           getFilePathFromType(filelist.at(i)->childType, filelist.at(i)->fileName);
        }
        daoError = qx::dao::update(filelist, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("更新文件路径失败: " + daoError.text().toStdString());
        }
    }

    QFileInfo fileInfo(srcProjectPathTemp);
    QString newSrcFile = desDirPath + QDir::separator() + fileInfo.fileName() + ".proj";

    QFile::rename(newSrcFile, destProjectPathTemp);
    // qInfo() << __FILE__ << __func__ << __LINE__ << "srcProjectPathTemp : " << srcProjectPathTemp << "destProjectPathTemp : " << destProjectPathTemp;
    // qInfo() << __FILE__ << __func__ << __LINE__ << "newSrcFile : " << newSrcFile;
    saveFile(destProjectPathTemp);
    //
    // 另存为完成后打开新项目
    openProject(destProjectPathTemp);

    return true;
}

QJsonObject ProjectAndFileManage::getCurrentProjectInfo()
{
    if (!currentProject.isNull())
    {
        return currentProject->toJson();
    }
    return QJsonObject(); // 如果没有当前项目，返回空数组
}

// 基本完整性检查 检查数据库 文件管理数据结构的表 中的文件是否与项目目录中一致
//"C:/Users/<USER>/Documents/PJIC/build-idev1_mainapp-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/test20240130/test20240130.proj"
QString ProjectAndFileManage::projectBasicCheck(const QString &projectPath)
{
    // 检查.proj文件是否存在
    QFile file(projectPath);
    if (!file.exists())
    {
        // qInfo() << "projectPath : " << projectPath;
        LOG_ERROR_DEFAULT("project file not exists");
        return QString("project file not exists");
    }
    // 提取项目路径
    QFileInfo fileInfo(projectPath);
    QString projectPathTemp = fileInfo.absolutePath();
    // qInfo() << "fileInfo.absolutePath() " << fileInfo.absolutePath();

    // 检查manage.db文件是否存在
    QString manageDbPath = projectPathTemp + QDir::separator() + "data" + QDir::separator() + "manage.db";
    QFile manageDb(manageDbPath);
    if (!manageDb.exists())
    {
        // qInfo() << "manageDbPath : " << manageDbPath;
        LOG_ERROR_DEFAULT("manage.db file not exists");
        return QString("manage.db file not exists");
    }
    // qInfo() << "manageDbPath : " << manageDbPath;
    // 连接数据库
    SqlOrm::instance().databaseConnect(manageDbPath, "Password123.");
    QSqlDatabase db = SqlOrm::instance().getDatabase(manageDbPath);
    init(db);
    // 读取FileList表获取所有文件的路径
    QList<QSharedPointer<FileList>> list;
    qx_query query;
    QSqlError daoError = qx::dao::fetch_all(list, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all FileList :" + daoError.text().toStdString());
    }
    // 检查FileList表中的每个文件是否存在
    for (int i = 0; i < list.size(); i++)
    {
        FileList fileNode;
        fileNode.id = list.at(i)->id;
        QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
        if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
        {
            LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        }
        QString currentDeviceFolderPath = getDeviceFolderPath(fileNode.projectTreeParentNodeId);
        // 根据fileType获取文件路径
        QString filePath = getFilePathFromType(fileNode.childType, fileNode.fileName);
        // qInfo() << "filePath : " << filePath;
        // 完整路径
        filePath = currentDeviceFolderPath + QDir::separator() + filePath;
        // qInfo() << "filePath : " << filePath;
        QFile file(filePath);
        if (!file.exists())
        {
            LOG_ERROR_DEFAULT("file not exists:" + filePath.toStdString());
            return QString("file not exists");
        }
    }
    return QString();
}

// 当前项目完整性检查
QString ProjectAndFileManage::currentProjectCheck()
{
    if (currentProject.isNull())
    {
        LOG_ERROR_DEFAULT("currentProject is null");
        return QString("currentProject is null");
    }
    QString projectPath = currentProject->getPath() + QDir::separator() + currentProject->getName() + ".proj";
    return projectBasicCheck(projectPath);
}

// SHA256 名称+路径+版本+创建时间
void ProjectAndFileManage::protect() // 保护工程
{
    if (currentProject.isNull())
    {
        return;
    }
    QString name = currentProject->getName();
    QString path = currentProject->getPath();
    QString version = currentProject->getVersion();
    QString createTime = currentProject->getCreateTime();
    QString str = name + path + version + createTime;
    QByteArray ba = str.toUtf8();
    QByteArray hash = QCryptographicHash::hash(ba, QCryptographicHash::Sha256);
    QString sha256 = hash.toHex();
    currentProject->setValue("Protection", sha256);
    // qInfo() << __func__ << __LINE__ << "currentProjectName : " << currentProjectName;
    saveFile(currentProjectName);
}

// 字符串赋空
void ProjectAndFileManage::deprotect() // 解除保护
{
    if (currentProject.isNull())
    {
        return;
    }
    currentProject->setValue("Protection", "");
    // qInfo() << __func__ << __LINE__ << "currentProjectName : " << currentProjectName;
    saveFile(currentProjectName);
}

// 获取当前项目路径
QString ProjectAndFileManage::getCurrentProjectPath()
{
    // 从 D:/ee/ee.proj 中获取 D:/ee
    currentProjectPath = currentProjectName;

    QFileInfo fileInfo(currentProjectPath);
    QString currentProjectPath = fileInfo.absolutePath();
    // qInfo() << "currentProjectName : " << currentProjectName;
    // qInfo() << "currentProjectPath : " << currentProjectPath;

    return currentProjectPath;
}

// 获取当前项目名称
QString ProjectAndFileManage::getCurrentProjectName()
{
    return currentProjectName;
}

// 获取当前节点属于哪个设备节点
QString ProjectAndFileManage::getDeviceName(const long &nodeId)
{
    // 获取当前节点的父节点，判断父节点是否是设备节点，如果是则返回父节点nodeName
    // 如果不是则递归调用getDeviceName
    // 如果一直递归到根节点还没有找到设备节点，则返回空字符串
    if (nodeId == 0)
    {
        return QString();
    }
    ProjectTree projectTreeNode;
    projectTreeNode.id = nodeId;
    QSqlError daoError = qx::dao::fetch_by_id(projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
        return QString();
    }
    return projectTreeNode.deviceName;
}

// 8-12
// 24 22 20 18 16
// 10(1000) + 6(300) + 1(10) + 4(4)
// 将某个节点设置为IsSelected设置为true,其它节点设置为false
bool ProjectAndFileManage::setSelectedNode(const long &nodeId)
{
    QList<QSharedPointer<ProjectTree>> projectTreeList;
    QSqlError daoError = qx::dao::fetch_all(projectTreeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all ProjectTree :" + daoError.text().toStdString());
    }
    for (int i = 0; i < projectTreeList.size(); i++)
    {
        if (projectTreeList.at(i)->id == nodeId)
        {
            projectTreeList.at(i)->isSelected = true;
        }
        else
        {
            projectTreeList.at(i)->isSelected = false;
        }
        daoError = qx::dao::update(projectTreeList.at(i), &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
        }
    }
    return true;
}

// 新建项目时调用copyProjectNode()从模板表中拷贝（不是从模板数据库中拷贝）所有调用类型为project的节点
void ProjectAndFileManage::copyProjectNode(const QString &projectName)
{
    // 从模板表中拷贝所有调用类型为project的节点
    // 需要替换根节点的nodeName为当前的projectName 替换根节点的直属子节点数的parentName为当前的projectName
    QList<QSharedPointer<ProjectTreeTemplate>> projectTreeTemplateList;
    qx_query query;
    query.where("calltype").isEqualTo("project");
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTreeTemplateList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTreeTemplate :" + daoError.text().toStdString());
    }

    for (int i = 0; i < projectTreeTemplateList.size(); i++)
    {
        ProjectTree projectTreeNode;
        projectTreeNode.parentId = projectTreeTemplateList.at(i)->parentId;
        projectTreeNode.parentName = projectTreeTemplateList.at(i)->parentName;
        projectTreeNode.nodeName = projectTreeTemplateList.at(i)->nodeName;
        projectTreeNode.nodeDescription = projectTreeTemplateList.at(i)->nodeDescription;
        projectTreeNode.nodeIcon = projectTreeTemplateList.at(i)->nodeIcon;
        projectTreeNode.nodeType = projectTreeTemplateList.at(i)->nodeType;
        projectTreeNode.eventName = projectTreeTemplateList.at(i)->eventName;
        projectTreeNode.haveMenu = projectTreeTemplateList.at(i)->haveMenu;
        projectTreeNode.totalChildNodeType = projectTreeTemplateList.at(i)->totalChildNodeType;
        projectTreeNode.count = projectTreeTemplateList.at(i)->count;
        projectTreeNode.sortNo = projectTreeTemplateList.at(i)->sortNo;
        projectTreeNode.isSelected = 0;
        projectTreeNode.isExtended = 0;

        if (projectTreeNode.parentId == 0)
        {
            projectTreeNode.nodeName = projectName;
        }
        else if (projectTreeNode.parentId == 1)
        {
            projectTreeNode.parentName = projectName;
        }
        daoError = qx::dao::insert(projectTreeNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("insert ProjectTree :" + daoError.text().toStdString());
        }
    }
}

// 获取某个模板节点的所有直属子节点
QList<QSharedPointer<ProjectTreeTemplate>> ProjectAndFileManage::getAllTempChildNodes(const long &nodeId)
{
    QList<QSharedPointer<ProjectTreeTemplate>> projectTreeTemplateList;
    qx_query query;
    query.where("parentId").isEqualTo(QVariant::fromValue(nodeId));
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTreeTemplateList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTreeTemplate :" + daoError.text().toStdString());
    }
    return projectTreeTemplateList;
}

// 递归将模板表中的部分节点拷贝到某个节点下
void ProjectAndFileManage::setAllChildNode(const QList<QSharedPointer<ProjectTreeTemplate>> &projectTreeTemplateList,
                                           const long &nodeId, const QString &deviceName)
{
    for (int i = 0; i < projectTreeTemplateList.size(); i++)
    {
        // 根据nodeId获取节点的parentName
        ProjectTree parentNode;
        parentNode.id = nodeId;
        QSqlError daoError = qx::dao::fetch_by_id(parentNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
        }
        ProjectTree projectTreeNode;
        projectTreeNode.parentId = parentNode.id;
        projectTreeNode.parentName = parentNode.nodeName;
        projectTreeNode.nodeName = projectTreeTemplateList.at(i)->nodeName;
        projectTreeNode.nodeDescription = projectTreeTemplateList.at(i)->nodeDescription;
        projectTreeNode.nodeIcon = projectTreeTemplateList.at(i)->nodeIcon;
        projectTreeNode.nodeType = projectTreeTemplateList.at(i)->nodeType;
        projectTreeNode.eventName = projectTreeTemplateList.at(i)->eventName;
        projectTreeNode.haveMenu = projectTreeTemplateList.at(i)->haveMenu;
        projectTreeNode.totalChildNodeType = projectTreeTemplateList.at(i)->totalChildNodeType;
        projectTreeNode.count = projectTreeTemplateList.at(i)->count;
        projectTreeNode.sortNo = projectTreeTemplateList.at(i)->sortNo;
        projectTreeNode.isSelected = false;
        projectTreeNode.isExtended = false;
        projectTreeNode.deviceName = deviceName;
        // 此时生成新节点的id
        daoError = qx::dao::insert(projectTreeNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("insert ProjectTree :" + daoError.text().toStdString());
        }
        // 获取子节点的所有直属子节点
        QList<QSharedPointer<ProjectTreeTemplate>> childNodeList = getAllTempChildNodes(projectTreeTemplateList.at(i)->id);
        // 根据parentId和nodeName获取新生成的节点的id
        qx_query query;
        query.where("parentId").isEqualTo(QVariant::fromValue(projectTreeNode.parentId)).and_("nodeName").isEqualTo(
                    projectTreeNode.nodeName);
        daoError = qx::dao::fetch_by_query(query, projectTreeNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
        }
        // 递归设置子节点的所有直属子节点，传入新生产的节点的id
        setAllChildNode(childNodeList, projectTreeNode.id, deviceName);
    }
}

// 根据新建设备组态文件夹名字创建文件夹及子文件夹
bool ProjectAndFileManage::createNewDeviceFolderName(const QString &deviceName)
{
    // D:/设备组态文件夹
    currentFilePath = getCurrentProjectPath();

    // 在当前项目路径下创建设备文件夹
    QDir dir(currentFilePath);
    if (!dir.exists(deviceName))
    {
        if (!dir.mkdir(deviceName))
        {
            LOG_ERROR_DEFAULT("mkdir error");
            return false;
        };
    }
    // 在当前项目路径下创建User目录存放用户文件
    if (!dir.exists(deviceName + QDir::separator() + "User"))
    {
        if (!dir.mkdir(deviceName + QDir::separator() + "User"))
        {
            LOG_ERROR_DEFAULT("mkdir error");
            return false;
        }
    }
    // 在当前项目路径下创建Core1目录存放程序文件
    if (!dir.exists(deviceName + QDir::separator() + "User" + QDir::separator() + "Core1"))
    {
        if (!dir.mkdir(deviceName + QDir::separator() + "User" + QDir::separator() + "Core1"))
        {
            LOG_ERROR_DEFAULT("mkdir error");
            return false;
        }
    }
    // 在当前项目路径下创建UserFBs目录下存放功能块文件和函数文件
    if (!dir.exists(deviceName + QDir::separator() + "User" + QDir::separator() + "UserFBs"))
    {
        if (!dir.mkdir(deviceName + QDir::separator() + "User" + QDir::separator() + "UserFBs"))
        {
            LOG_ERROR_DEFAULT("mkdir error");
            return false;
        }
    }
    return true;
}

// 新建设备组态时调用addDeviceNode 从ProjectTreeTemplate表中拷贝所有调用类型为device的节点
// 需要替换parentId parentName nodeName
// 替换parentId为项目名称的id,替换parentName为项目名称, 替换nodeName为deviceName
bool ProjectAndFileManage::addDeviceNode(const QString &deviceName)
{
    currentFilePath = getCurrentProjectPath();

    // 根据deviceName查询ProjectTree表中是否有同名设备
    qx_query query;
    query.where("nodeName").isEqualTo(deviceName);
    ProjectTree tempNode;
    QSqlError daoError = qx::dao::fetch_by_query(query, tempNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
    }
    if (tempNode.parentId == 1)
    {
        LOG_ERROR_DEFAULT("device already exists");
        return false;
    }

    // 新建设备组态文件夹及子文件夹
    if (!createNewDeviceFolderName(deviceName))
    {
        LOG_ERROR_DEFAULT("createNewDeviceFolderName error");
        return false;
    }
    // 查询ProjectTreeTemplate表中"新建设备"的根节点
    ProjectTreeTemplate projectTreeTemplate;
    // 此处已写死，后期需要修改
    // daoError = qx::dao::fetch_by_query(query, projectTreeTemplate, &m_db);
    projectTreeTemplate.id = 5;
    daoError = qx::dao::fetch_by_id(projectTreeTemplate, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTreeTemplate :" + daoError.text().toStdString());
    }
    // 根据根节点的id查询ProjectTree表中根节点的nodeName
    ProjectTree projectTreeRoot;
    projectTreeRoot.id = 1;
    daoError = qx::dao::fetch_by_id(projectTreeRoot, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
    }
    // 在ProjectTree表中插入新建设备的根节点
    ProjectTree projectTreeDeviceRoot;
    projectTreeDeviceRoot.parentId = projectTreeRoot.id;
    projectTreeDeviceRoot.parentName = projectTreeRoot.nodeName;
    projectTreeDeviceRoot.nodeName = deviceName;
    projectTreeDeviceRoot.nodeDescription = projectTreeTemplate.nodeDescription;
    projectTreeDeviceRoot.nodeIcon = projectTreeTemplate.nodeIcon;
    projectTreeDeviceRoot.nodeType = projectTreeTemplate.nodeType;
    projectTreeDeviceRoot.eventName = projectTreeTemplate.eventName;
    projectTreeDeviceRoot.haveMenu = projectTreeTemplate.haveMenu;
    projectTreeDeviceRoot.totalChildNodeType = projectTreeTemplate.totalChildNodeType;
    projectTreeDeviceRoot.count = projectTreeTemplate.count;
    projectTreeDeviceRoot.sortNo = projectTreeTemplate.sortNo;
    projectTreeDeviceRoot.isSelected = false;
    projectTreeDeviceRoot.isExtended = false;
    projectTreeDeviceRoot.deviceName = deviceName;
    daoError = qx::dao::insert(projectTreeDeviceRoot, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("insert ProjectTree :" + daoError.text().toStdString());
    }

    // 根据parentId和nodeName获取新生成的节点的id
    query.clear();
    query.where("parentId").isEqualTo(QVariant::fromValue(projectTreeDeviceRoot.parentId)).and_("nodeName").isEqualTo(
                projectTreeDeviceRoot.nodeName);

    daoError = qx::dao::fetch_by_query(query, projectTreeDeviceRoot, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
    }
    // 递归拷贝节点到数据库
    setAllChildNode(getAllTempChildNodes(projectTreeTemplate.id), projectTreeDeviceRoot.id, deviceName);

    // 将所有节点的isSelected设置为false
    QList<QSharedPointer<ProjectTree>> projectTreeList;
    daoError = qx::dao::fetch_all(projectTreeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all ProjectTree :" + daoError.text().toStdString());
    }
    for (int i = 0; i < projectTreeList.size(); i++)
    {
        projectTreeList.at(i)->isSelected = 0;
        daoError = qx::dao::update(projectTreeList.at(i), &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
        }
    }
    // 将新建设备的根节点的isSelected设置为true
    projectTreeDeviceRoot.isSelected = 1;
    daoError = qx::dao::update(projectTreeDeviceRoot, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
    }
    // 在HardwareConfig表中添加新配置
    for (auto it : m_hardwareConfigTemplate)
    {
        // hardwareConfig.settingName = deviceName;
        it->settingName = deviceName;
        QSqlError daoError = qx::dao::insert(it, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("insert HardwareConfig :" + daoError.text().toStdString());
        }
    }
    return true;
}

// 删除设备节点时将id号最小的EventName为Device的节点的IsSelected设置为true,
// 删除项目时删除数据库和文件夹，删除设备节点时，删除文件夹
bool ProjectAndFileManage::deleteDeviceNode(const long &nodeId)
{
    // 判断要删除的设备节点isSelected是否为true
    ProjectTree projectTree;
    projectTree.id = nodeId;
    QSqlError daoError = qx::dao::fetch_by_id(projectTree, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
    }
    // 如果isSelected为true,则将id号最小的EventName为Device的节点的IsSelected设置为true
    if (projectTree.isSelected != 0)
    {
        LOG_ERROR_DEFAULT("isSelected is true");
        qx_query query;
        query.where("eventName").isEqualTo("[Device]");
        QList<QSharedPointer<ProjectTree>> projectTreeList;
        daoError = qx::dao::fetch_by_query(query, projectTreeList, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
        }
        if (projectTreeList.size() == 0)
        {
            LOG_ERROR_DEFAULT("projectTreeList is empty");
        }
        projectTreeList.at(0)->isSelected = 1;
        daoError = qx::dao::update(projectTreeList.at(0), &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
        }
    }
    // 删除设备节点文件夹
    QString currentDeviceFolderPath = getDeviceFolderPath(nodeId);
    QDir dir(currentDeviceFolderPath);
    if (!dir.removeRecursively())
    {
        LOG_ERROR_DEFAULT("removeRecursively error");
        return false;
    }
    // 删除设备节点
    deleteProjectTreeNode(nodeId);
    // 在HardwareConfig表中删除配置
    qx_query query;
    query.where("settingName").isEqualTo(projectTree.nodeName);
    QList<QSharedPointer<HardwareConfig>> hardwareConfigList;
    daoError = qx::dao::fetch_by_query(query, hardwareConfigList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query HardwareConfig :" + daoError.text().toStdString());
    }
    for (int i = 0; i < hardwareConfigList.size(); i++)
    {
        daoError = qx::dao::delete_by_id(hardwareConfigList.at(i), &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("delete_by_id HardwareConfig :" + daoError.text().toStdString());
        }
    }
    // 在FILeList表中删除SettingName为projectTree.nodeName的所有文件
    query.clear();
    query.where("SettingName").isEqualTo(projectTree.nodeName);
    QList<FileList> fileListTemp;
    daoError = qx::dao::fetch_by_query(query, fileListTemp, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
    }
    for (int i = 0; i < fileListTemp.size(); i++)
    {
        FileList temp = fileListTemp.at(i);
        daoError = qx::dao::delete_by_id(temp, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("delete_by_id FileList :" + daoError.text().toStdString());
        }
    }

    return true;
}

QString ProjectAndFileManage::getCurrentDeviceName()
{
    QList<QSharedPointer<ProjectTree>> projectTreeList;
    QSqlError daoError = qx::dao::fetch_all(projectTreeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all ProjectTree :" + daoError.text().toStdString());
    }
    for (int i = 0; i < projectTreeList.size(); i++)
    {
        if (projectTreeList.at(i)->isSelected == 1)
        {
            return projectTreeList.at(i)->nodeName;
        }
    }
    return QString();
}

QString ProjectAndFileManage::getNewDeviceName()
{
    QList<QSharedPointer<ProjectTree>> projectTreeList;
    QSqlError daoError = qx::dao::fetch_all(projectTreeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all ProjectTree :" + daoError.text().toStdString());
    }

    QString namePrefix = "PLC_", deviceName;
    int deviceId = 1, nameSuffix;
    for (int i = 0; i < projectTreeList.size(); i++)
    {
        deviceName = projectTreeList.at(i)->deviceName;
        if (deviceName.startsWith(namePrefix))
        {
            deviceName = deviceName.split(namePrefix).at(1);
            nameSuffix = deviceName.toInt();
            if (nameSuffix >= deviceId)
            {
                deviceId = ++nameSuffix;
            }
        }
    }

    QString newDeviceIdStr = QString::number(deviceId);
    QString newDeviceName = namePrefix + (deviceId > 10 ? newDeviceIdStr : "0" + newDeviceIdStr);

    return newDeviceName;
}

// 获取某设备下的所有文件及属性
QVector<QStringList> ProjectAndFileManage::getDeviceAllFiles(const QString &deviceName)
{
    qx_query query;
    query.where("SettingName").isEqualTo(deviceName);
    QList<FileList> fileListTemp;
    QSqlError daoError = qx::dao::fetch_by_query(query, fileListTemp, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query HardwareConfig :" + daoError.text().toStdString());
    }
    QVector<QStringList> deviceFiles;
    for (int i = 0; i < fileListTemp.size(); i++)
    {
        QStringList deviceFile;
        deviceFile.append(QString::number(fileListTemp.at(i).id));
        deviceFile.append(fileListTemp.at(i).fileName);
        deviceFile.append(fileListTemp.at(i).fatherType);
        deviceFile.append(fileListTemp.at(i).childType);
        deviceFile.append(fileListTemp.at(i).fileIcon);
        deviceFile.append(QString::number(fileListTemp.at(i).projectTreeNodeId));
        deviceFile.append(QString::number(fileListTemp.at(i).projectTreeParentNodeId));
        deviceFile.append(fileListTemp.at(i).createTime);
        deviceFile.append(fileListTemp.at(i).lastModifyTime);
        deviceFile.append(fileListTemp.at(i).havePassword);
        deviceFile.append(fileListTemp.at(i).dataType);
        deviceFile.append(fileListTemp.at(i).settingName);
        deviceFile.append(QString::number(fileListTemp.at(i).executionOrder));
        deviceFile.append(fileListTemp.at(i).executionType);
        deviceFile.append(fileListTemp.at(i).absolutePath);
        deviceFiles.append(deviceFile);
    }
    return deviceFiles;
}

// 添加项目树节点
void ProjectAndFileManage::addProjectTreeNode(const long &parentNodeId, const QString &nodeName,
                                              const QString &nodeDescription, const QString &nodeIcon,
                                              const QString &nodeType, const QString &eventName,
                                              const bool &haveMenu, const QString &totalChildNodeType,
                                              const QString &count, const QString &deviceName)
{
    ProjectTree parentNode;
    parentNode.id = parentNodeId;
    QSqlError daoError = qx::dao::fetch_by_id(parentNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
    }
    ProjectTree node;
    node.parentId = parentNode.id;
    node.parentName = parentNode.nodeName;
    node.nodeName = nodeName;
    node.nodeDescription = nodeDescription;
    node.nodeIcon = nodeIcon;
    node.nodeType = nodeType;
    node.eventName = eventName;
    node.haveMenu = haveMenu;
    node.totalChildNodeType = totalChildNodeType;
    node.count = count;
    node.sortNo = parentNode.sortNo + 1;
    node.isSelected = false;
    node.isExtended = false;
    node.deviceName = deviceName;
    node.deviceName = parentNode.deviceName;
    daoError = qx::dao::insert(node, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("insert ProjectTree :" + daoError.text().toStdString());
    }
}

// 删除项目树节点，如果项目树节点下有子节点，一并递归删除
void ProjectAndFileManage::deleteProjectTreeNode(const long &nodeId)
{
    ProjectTree node;
    node.id = nodeId;
    QSqlError daoError = qx::dao::fetch_by_id(node, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
    }
    qx_query query;
    query.where("parentId").isEqualTo(QVariant::fromValue(node.id));
    QList<QSharedPointer<ProjectTree>> childNodeList;
    daoError = qx::dao::fetch_by_query(query, childNodeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
    }
    for (int i = 0; i < childNodeList.size(); i++)
    {
        deleteProjectTreeNode(childNodeList.at(i)->id);
    }
    daoError = qx::dao::delete_by_id(node, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete_by_id ProjectTree :" + daoError.text().toStdString());
    }
}

// 获取某个节点的所有直属子节点
QList<QSharedPointer<ProjectTree>> ProjectAndFileManage::getAllChildNodes(const long &nodeId)
{
    qx_query query;
    query.where("parentId").isEqualTo(QVariant::fromValue(nodeId));
    QList<QSharedPointer<ProjectTree>> childNodeList;
    QSqlError daoError = qx::dao::fetch_by_query(query, childNodeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
    }
    return childNodeList;
}

// 获取当前节点下的某类型的直属子节点数
int ProjectAndFileManage::FileCount(const long &parentNodeId, const QString &parentNodeType)
{
    qx_query query;
    query.where("parentId").isEqualTo(QVariant::fromValue(parentNodeId)).and_("nodeType").isEqualTo(parentNodeType);
    QList<QSharedPointer<ProjectTree>> childNodeList;
    QSqlError daoError = qx::dao::fetch_by_query(query, childNodeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
    }
    return childNodeList.size();
}

// 新添加文件后 刷新树上的每一个节点数据
// 返回ID 节点名称 节点类型 直属子节点数
QJsonArray ProjectAndFileManage::refreshThree()
{
    // 获取所有节点并按照SortNo降序排序
    qx_query query;
    query.orderAsc("sortNo");
    QList<QSharedPointer<ProjectTree>> projectTreeList;
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTreeList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all ProjectTree :" + daoError.text().toStdString());
    }

    // 遍历所有totalChildNodeType不为空的节点，则根据totalChildNodeType统计直属子节点数
    // totalChildNodeType为空 或者folder目录 file文件 command 命令等

    // 根据totalChildNodeType统计直属子节点数
    for (int i = 0; i < projectTreeList.size(); i++)
    {
        if (!projectTreeList.at(i)->totalChildNodeType.isEmpty())
        {
            if (projectTreeList.at(i)->totalChildNodeType == "file")
            {
                projectTreeList.at(i)->count = QString::number(FileCount(projectTreeList.at(i)->id, "file"));
            }
            else if (projectTreeList.at(i)->totalChildNodeType == "folder")
            {
                projectTreeList.at(i)->count = QString::number(FileCount(projectTreeList.at(i)->id, "folder"));
            }
            else
            {
                projectTreeList.at(i)->count = QString::number(FileCount(projectTreeList.at(i)->id, "command"));
            }
            daoError = qx::dao::update(projectTreeList.at(i), &m_db);
            if (daoError.type() != QSqlError::NoError)
            {
                LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
            }
        }
    }
    QJsonArray jsonArray;
    for (int i = 0; i < projectTreeList.size(); i++)
    {
        QJsonObject jsonObject;
        jsonObject.insert("id", QString::number(projectTreeList.at(i)->id));
        jsonObject.insert("parentId", QString::number(projectTreeList.at(i)->parentId));
        jsonObject.insert("parentName", projectTreeList.at(i)->parentName);
        jsonObject.insert("nodeName", projectTreeList.at(i)->nodeName);
        jsonObject.insert("nodeDescription", projectTreeList.at(i)->nodeDescription);
        jsonObject.insert("nodeIcon", projectTreeList.at(i)->nodeIcon);
        jsonObject.insert("nodeType", projectTreeList.at(i)->nodeType);
        jsonObject.insert("eventName", projectTreeList.at(i)->eventName);
        jsonObject.insert("haveMenu", projectTreeList.at(i)->haveMenu);
        jsonObject.insert("totalChildNodeType", projectTreeList.at(i)->totalChildNodeType);
        jsonObject.insert("count", projectTreeList.at(i)->count);
        jsonObject.insert("sortNo", projectTreeList.at(i)->sortNo);
        jsonObject.insert("isSelected", projectTreeList.at(i)->isSelected);
        jsonObject.insert("isExtended", projectTreeList.at(i)->isExtended);
        jsonArray.append(jsonObject);
    }
    return jsonArray;
}

// 检查文件名是否合法
bool ProjectAndFileManage::isFileNameValid(const QString &fileName)
{
    // qInfo() << "fileName : " << fileName;
    // 检查文件名的后缀是否为 .CFC .FBD .IL .LD .sfc .st .ds .r //小写后缀文件在第二版中已经存入数据库
    QStringList suffixList = { ".CFC", ".FBD", ".IL", ".LD", ".SFC", ".ST", ".C", ".PY", "r" };
    QString suffix = "." + QFileInfo(fileName).suffix();
    // Qt::CaseInsensitive表示不区分大小写
    if (!suffixList.contains(suffix, Qt::CaseInsensitive))
    {
        LOG_ERROR_DEFAULT("suffix error" + suffix.toStdString());
        return false;
    }
    // 不可包含非法字符
    QStringList illegalList = { "\\", "/", ":", "*", "?", "\"", "<", ">", "|" };
    for (int i = 0; i < illegalList.size(); i++)
    {
        if (fileName.contains(illegalList[i]))
        {
            LOG_ERROR_DEFAULT("fileName contains illegal character" + illegalList[i].toStdString());
            return false;
        }
    }
    // 文件名不可为 cfc CFC fbd FBD il IL ld LD sfc SFC st ST ds DS r R
    // 文件名为去除后缀的文件名
    QStringList fileTypeList = { "cfc", "CFC", "fbd", "FBD", "il", "IL", "ld", "LD", "sfc", "SFC", "st", "ST", "C", "c", "PY", "py", "ds", "DS", "r", "R" };
    QString fileNameWithoutSuffix = QFileInfo(fileName).baseName();
    if (fileTypeList.contains(fileNameWithoutSuffix))
    {
        LOG_ERROR_DEFAULT("fileName error" + fileName.toStdString());
        return false;
    }
    return true;
}

// 检查fatherType是否合法
bool ProjectAndFileManage::isFatherTypeValid(const QString &fatherType)
{
    // 检查fatherType是否为"CFC"、"FBD"、"IL"、"LD"、"SFC"、"ST"、"ds"、"r"
    QStringList fatherTypeList = { "CFC", "FBD", "IL", "LD", "SFC", "ST", "C", "PY", "r" };
    if (!fatherTypeList.contains(fatherType, Qt::CaseInsensitive))
    {
        LOG_ERROR_DEFAULT("fatherType error" + fatherType.toStdString());
        return false;
    }
    return true;
}

// 检查childType是否合法
bool ProjectAndFileManage::isChildTypeValid(const QString &childType)
{
    // childType为的格式为FBD_FunctionBlock
    // 判断childType是否为{"FunctionBlock", "Function", "Program", "DataBlock", "Resource"}
    QStringList childTypeList = { "FUNCTIONBLOCK", "FUNCTION", "PROGRAM", "RESOURCEGLOBALX", "DIRECTGLOBALX", "TYPEX", "WATCHLISTX" };
    for (int i = 0; i < childTypeList.size(); i++)
    {
        if (childType.contains(childTypeList[i]))
        {
            return true;
        }
    }
    LOG_ERROR_DEFAULT("childType error" + childType.toStdString());
    return false;
}

// 检查fatherNodeId是否在项目树中
bool ProjectAndFileManage::isFatherNodeIdValid(const long fatherNodeId)
{
    ProjectTree fatherNode;
    fatherNode.id = fatherNodeId;
    QSqlError daoError = qx::dao::fetch_by_id(fatherNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
    }
    if (fatherNode.parentName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fatherNodeId error" + QString::number(fatherNodeId).toStdString());
        return false;
    }
    return true;
}

// 检查某个文件下个是否挂载某文件,  存在返回true 不存在返回false
bool ProjectAndFileManage::isFileExist(QString &filePath, const QString &fileName, const long &projectTreeParentNodeId)
{
    handleString(filePath);
    // 在文件中判断文件是否存在
    if (QFileInfo(filePath).exists())
    {
        LOG_ERROR_DEFAULT("file already exists");
        return true;
    }
    // 在数据库中判断文件是否存在
    qx_query query;
    query.where("projectTreeParentNodeId").isEqualTo(QVariant::fromValue(projectTreeParentNodeId)).and_("fileName").isEqualTo(
                fileName);
    FileList fileNode;
    QSqlError daoError = qx::dao::fetch_by_query(query, fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
    }
    if (fileNode.fileName == fileName)
    {
        LOG_ERROR_DEFAULT("file already exists");
        return true;
    }
    return false;
}

// 根据fileType和fileName拼接文件路径
// "User\\UserFBs\\testFile.cfc"
QString ProjectAndFileManage::getFilePathFromType(const QString &childType, const QString &fileName)
{
    // qInfo() << "childType : " << childType << "fileName : " << fileName;
    QString filePath;

    if (childType == "FUNCTIONBLOCK" || childType == "FUNCTION")
    {
        filePath = QString("User") + QDir::separator() + QString("UserFBs") + QDir::separator() + fileName;
    }
    else if (childType == "PROGRAM")
    {
        filePath = QString("User") + QDir::separator() + QString("Core1") + QDir::separator() + fileName;
    }
    else
    {
        filePath = QString("User") + QDir::separator() + fileName;
    }
    return filePath;
}

//"C:/Users/<USER>/Documents/PJIC/build-idev1_mainapp-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/test20240130\\PLC01"
QString ProjectAndFileManage::getDeviceFolderPath(const long &ProjectTreeNodeId)
{
    /*     // 根据ProjectTreeParentNodeId获取父节点的nodeName
        ProjectTree parentNode;
        parentNode.id = ProjectTreeNodeId;
        QSqlError daoError = qx::dao::fetch_by_id(parentNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
        } */
    // 根据parentNode的nodeName获取当前设备组态文件夹的路径
    QString currentDeviceFolderPath = getCurrentProjectPath() + QDir::separator() + getDeviceName(ProjectTreeNodeId);
    return currentDeviceFolderPath;
}

long ProjectAndFileManage::getProjectTreeParentNodeId(const QString &deviceName, const QString &eventName)
{
    qx_query query;
    query.where("deviceName").isEqualTo(deviceName).and_("eventName").isEqualTo(eventName);
    ProjectTree projectTree;
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTree, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch getProjectTreeParentNodeId :" + deviceName.toStdString() + " " + eventName.toStdString() + " " +
                          daoError.text().toStdString());
        return 0L;
    }
    return projectTree.parentId;
}
long ProjectAndFileManage::getProjectTreeNodeId(const QString &deviceName, const QString &eventName)
{
    qx_query query;
    query.where("deviceName").isEqualTo(deviceName).and_("eventName").isEqualTo(eventName);
    ProjectTree projectTree;
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTree, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch getProjectTreeParentNodeId :" + deviceName.toStdString() + " " + eventName.toStdString() + " " +
                          daoError.text().toStdString());
        return 0L;
    }
    return projectTree.id;
}

QJsonObject ProjectAndFileManage::getFileInfoFromProjectTreeID(const long &projectTreeID)
{
    qx_query query;
    query.where("ProjectTreeNodeId").isEqualTo(QVariant::fromValue(projectTreeID));
    FileList fileList;
    QSqlError daoError = qx::dao::fetch_by_query(query, fileList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return QJsonObject();
    }
    return fileList.ToJson();
}

// 添加不在数据库记录中的文件，添加到数据库中
bool ProjectAndFileManage::addFile(const QString &fileName, const QString &fatherType, const QString &childType,
                                   const QString &fileIcon, const long &projectTreeParentNodeId, const QString &havePassword,
                                   const QString &dataType, const QString &settingName, const int &executionOrder, const QString &textContent)

{
    // 根据fileType获取文件路径 \\User\\UserFBs\\testFile.cfc
    QString filePath = getFilePathFromType(childType, fileName);
    // LOG_INFO_DEFAULT("filePath : " + filePath.toStdString());

    QString currentDeviceFolderPath = getDeviceFolderPath(projectTreeParentNodeId);
    // LOG_INFO_DEFAULT("projectTreeParentNodeId : " + QString::number(projectTreeParentNodeId).toStdString());
    // LOG_INFO_DEFAULT("currentDeviceFolderPath : " + currentDeviceFolderPath.toStdString());
    // 完整路径
    filePath = currentDeviceFolderPath + QDir::separator() + filePath;

    if (!isFileNameValid(fileName) || !isFatherTypeValid(fatherType) || !isChildTypeValid(childType) ||
        !isFatherNodeIdValid(projectTreeParentNodeId) || isFileExist(filePath, fileName, projectTreeParentNodeId))
    {
        LOG_ERROR_DEFAULT("file is invalid");
        return false;
    }

    // 在本地创建文件, 并写入初始化内容
    // C:/Users/<USER>/Documents/PJIC/build-idev1_mainapp-Desktop_Qt_5_15_2_MSVC2019_64bit-Release/test20240130\\PLC01\\UserFBs\\testFile.cfc
    QFile file(filePath);
    if (!file.open(QIODevice::WriteOnly))
    {
        LOG_ERROR_DEFAULT("open error : " + filePath.toStdString());
        return false;
    }
    QTextStream out(&file);
    if (!textContent.isEmpty())
    {
        out << textContent;
    }
    file.close();

    // 添加ProjectTree节点
    addProjectTreeNode(projectTreeParentNodeId, fileName, QString(), fileIcon, "file", "[" + fatherType + "_" + childType + "]",
                       true, QString(), QString(), settingName);
    // 根据projectTreeParentNodeId和fileName获取新生成的节点的id
    qx_query query;
    query.where("parentId").isEqualTo(QVariant::fromValue(projectTreeParentNodeId)).and_("nodeName").isEqualTo(fileName);
    ProjectTree projectTreeNode;
    QSqlError daoError = qx::dao::fetch_by_query(query, projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query ProjectTree :" + daoError.text().toStdString());
    }
    // 添加FileList节点
    FileList fileList;
    fileList.fileName = fileName;
    fileList.fatherType = fatherType;
    fileList.childType = childType;
    fileList.fileIcon = fileIcon;
    fileList.projectTreeNodeId = projectTreeNode.id;
    fileList.projectTreeParentNodeId = projectTreeNode.parentId;
    fileList.createTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    fileList.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    fileList.havePassword = havePassword;
    fileList.dataType = dataType;
    fileList.settingName = settingName;
    fileList.executionOrder = (childType == "PROGRAM" ? (getMaxExecutionOrderFromTask(settingName, "T2") + 1) : 0);
    fileList.executionType = (childType == "PROGRAM" ? "T2" : "");
    fileList.absolutePath = filePath;
    daoError = qx::dao::insert(fileList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("insert FileList :" + daoError.text().toStdString());
    }
    return true;
}

// 目前实现不安全：后期实现解析文件内容来为判断文件类型是否允许添加和解析文件应该存放的路径
// 添加已有文件, 由用户手动复制到当前打开的项目下
// param1 :为用户复制的文件路径 + 文件名
// param1 :C:\Users\<USER>\Documents\PJIC\build-idev1_mainapp-Desktop_Qt_5_15_2_MSVC2019_64bit-Release\test20240140\PLC01\UserFBs\testFile.cfc
bool ProjectAndFileManage::addExistFile(QString &filePath, const QString &childType, const QString &fileIcon,
                                        const long &projectTreeParentNodeId, const QString &havePassword,
                                        const QString &dataType, const QString &settingName, const int &executionOrder)
{
    // 解析文件路径，获取文件名
    handleString(filePath);
    // qInfo() << "filePath : " << filePath;
    // 根据/分割字符串
    // QStringList strList = filePath.split("/");
    // QString fileName = strList.at(strList.size() - 1);
    QFileInfo fileInfo(filePath);
    QString fileName = fileInfo.fileName();
    // 获取fileName的后缀
    QString suffix = QFileInfo(fileName).suffix();
    // 将suffix全部转化为FatherType大写
    suffix = suffix.toUpper();
    QString fatherType = suffix;
    // qInfo() << "fileName : " << fileName;

    return addFile(fileName, fatherType, childType, fileIcon, projectTreeParentNodeId, havePassword, dataType, settingName,
                   executionOrder, QString());
}

// 删除文件 从本地和数据库中删除
bool ProjectAndFileManage::deleteFile(const long &fileListId)
{
    // 查询该文件是否存在
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    QString currentDeviceFolderPath = getDeviceFolderPath(fileNode.projectTreeParentNodeId);
    // qInfo() << "currentDeviceFolderPath " << currentDeviceFolderPath;
    // 根据fileType获取文件路径
    QString filePath = getFilePathFromType(fileNode.childType, fileNode.fileName);
    // 完整路径
    filePath = currentDeviceFolderPath + QDir::separator() + filePath;

    // 删除本地文件
    QFile file(filePath);
    if (!file.remove())
    {
        LOG_ERROR_DEFAULT("file not exist");
    }
    // 删除ProjectTree表中的记录
    ProjectTree projectTreeNode;
    projectTreeNode.id = fileNode.projectTreeNodeId;
    daoError = qx::dao::delete_by_id(projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete_by_id ProjectTree :" + daoError.text().toStdString());
    }
    // 删除FileList中的记录
    daoError = qx::dao::delete_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("delete_by_id FileList :" + daoError.text().toStdString());
    }
    return true;
}

// 根据
bool ProjectAndFileManage::deleteFileFromProjectTreeNode(const long &nodeId)
{
    // 从文件树节点中获取文件ID
    FileList fileNode;
    qx_query query;
    query.where("projectTreeNodeId").isEqualTo(QVariant::fromValue(nodeId));
    QSqlError daoError = qx::dao::fetch_by_query(query, fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    return deleteFile(fileNode.id);
}

// 打开文件
QString ProjectAndFileManage::openFile(const QString &fileKey)
{
    // 查询该文件是否存在
    // FileList fileNode;
    // fileNode.id = fileListId;
    // QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    // if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    // {
    //     LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
    //     return QString();
    // }
    // // 根据fileType获取文件路径
    // QString filePath = getFilePathFromType(fileNode.childType, fileNode.fileName);
    // QString currentDeviceFolderPath = getDeviceFolderPath(fileNode.projectTreeParentNodeId);
    // // 完整路径
    // filePath = currentDeviceFolderPath + "/" + filePath;
    // // LOG_INFO_DEFAULT("open filePath:" + filePath.toStdString());
    // //  打开文件
    // QFile file(filePath);
    // if (!file.open(QFile::ReadOnly))
    // {
    //     LOG_ERROR_DEFAULT("open error");
    //     return QString();
    // }
    // // 读取文件内容
    // QString textContent = file.readAll();
    // file.close();
    return STFileList[fileKey];
}

bool ProjectAndFileManage::readFile(const QString& deviceName, const QString& fileType, 
                                    const QString& fileName, const QString& filePath,
                                    const QString &textContent)
{
    STFileList.insert(filePath, textContent);
    return true;
}

QString ProjectAndFileManage::getFileData(const QString& fileKey)
{
    QString content;

    if (STFileList.contains(fileKey))
    {
        content = STFileList[fileKey];
    }
    
    return content;
}

// 更改某个节点的IsExtended
bool ProjectAndFileManage::setExtendedNode(const long &nodeId, const bool &isExtended)
{
    ProjectTree node;
    node.id = nodeId;
    QSqlError daoError = qx::dao::fetch_by_id(node, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
    }
    node.isExtended = isExtended;
    daoError = qx::dao::update(node, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
    }
    return true;
}

// 将数据保存到文件
bool ProjectAndFileManage::saveFile(const QString &fileKey, const QString &textContent)
{
    // 查询该文件是否存在
    // FileList fileNode;
    // fileNode.id = fileListId;
    // QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    // if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    // {
    //     LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
    //     return false;
    // }
    // // 根据fileType获取文件路径
    // QString filePath = getFilePathFromType(fileNode.childType, fileNode.fileName);
    // QString currentDeviceFolderPath = getDeviceFolderPath(fileNode.projectTreeParentNodeId);
    // // 完整路径
    // filePath = currentDeviceFolderPath + "/" + filePath;
    // // 保存文件
    // QFile file(filePath);
    // if (!file.open(QFile::WriteOnly))
    // {
    //     LOG_ERROR_DEFAULT("open error");
    //     return false;
    // }
    // file.write(textContent.toUtf8());
    // file.close();
    // // 更新FileList表中的lastModifyTime
    // fileNode.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    // daoError = qx::dao::update(fileNode, &m_db);
    // if (daoError.type() != QSqlError::NoError)
    // {
    //     LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
    // }

    if (STFileList.contains(fileKey))
    {
        STFileList[fileKey] = textContent;
    }

    return true;
}

// (在同项目下设备组态文件夹之间)将文件移动到指定路径下
bool ProjectAndFileManage::moveFile(const long &fileListId, const long &projectTreeParentNodeId)
{
    // 查询该源文件是否在数据库中存在
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    // 根据fileType获取文件路径
    QString filePath = getFilePathFromType(fileNode.childType, fileNode.fileName);
    QString currentDeviceFolderPath = getDeviceFolderPath(fileNode.projectTreeParentNodeId);
    // 完整路径
    filePath = currentDeviceFolderPath + filePath;
    // 在文件夹中判断源路径是否是否存在
    if (!QFileInfo(filePath).exists())
    {
        LOG_ERROR_DEFAULT("file not exists");
        return false;
    }
    addExistFile(filePath, fileNode.childType, fileNode.fileIcon, projectTreeParentNodeId,
                 fileNode.havePassword, fileNode.dataType, fileNode.settingName, fileNode.executionOrder);
    // 删除源文件和数据库中的记录
    deleteFile(fileListId);
    return true;
}

// 复制文件
bool ProjectAndFileManage::copyFile(const long &fileListId, const long &projectTreeParentNodeId)
{
    // 查询该源文件是否在数据库中存在
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    // 根据fileType获取文件路径
    QString filePath = getFilePathFromType(fileNode.childType, fileNode.fileName);
    QString currentDeviceFolderPath = getDeviceFolderPath(fileNode.projectTreeParentNodeId);
    // 完整路径
    filePath = currentDeviceFolderPath + filePath;

    // 在文件夹中判断源路径是否是否存在
    if (!QFileInfo(filePath).exists())
    {
        return false;
    }
    addExistFile(filePath, fileNode.childType, fileNode.fileIcon, projectTreeParentNodeId,
                 fileNode.havePassword, fileNode.dataType, fileNode.settingName, fileNode.executionOrder);
    return true;
}

// 重命名文件 重命名时检查是否有重名
// param2 : testFile (不带文件类型后缀)
bool ProjectAndFileManage::renameFile(const long &fileListId, const QString &newFileName)
{
    // 查询该源文件是否在数据库中存在
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    // 根据fileType获取文件路径
    QString filePath = getFilePathFromType(fileNode.childType, fileNode.fileName);
    QString currentDeviceFolderPath = getDeviceFolderPath(fileNode.projectTreeParentNodeId);
    // 完整路径
    filePath = currentDeviceFolderPath + filePath;
    QString suffix = QFileInfo(filePath).suffix();

    QString newFileNameTemp = newFileName + "." + suffix;
    // 检查文件名是否合法
    if (!isFileNameValid(newFileNameTemp))
    {
        return false;
    }
    // 从filePath中替换文件名为newFileName
    QString newFilePath = filePath;
    newFilePath.replace(QFileInfo(filePath).fileName(), newFileNameTemp);

    // 检查目的路径是否有重名文件
    if (QFileInfo(newFilePath).exists())
    {
        // qInfo() << "filepath : " << filePath;
        LOG_ERROR_DEFAULT("file exists");
        return false;
    }
    // 重命名文件
    if (!QFile::rename(filePath, newFilePath))
    {
        LOG_ERROR_DEFAULT("rename error");
        return false;
    }
    // 在数据库中更新文件名
    fileNode.fileName = newFileNameTemp;
    daoError = qx::dao::update(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
    }

    ProjectTree projectTreeNode;
    projectTreeNode.id = fileNode.projectTreeNodeId;
    daoError = qx::dao::fetch_by_id(projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id ProjectTree :" + daoError.text().toStdString());
    }
    projectTreeNode.nodeName = newFileNameTemp;
    daoError = qx::dao::update(projectTreeNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update ProjectTree :" + daoError.text().toStdString());
    }

    return true;
}

// 导入文件
bool ProjectAndFileManage::importFile(const QString &srcFilePath, const QString &destFilePath)
{
    return true;
}

// 导出文件
bool ProjectAndFileManage::exportFile(const QString &srcFilePath, const QString &destFilePath)
{
    return true;
}

// 加密文件 SHA256 名称+分类+子分类+创建时间
bool ProjectAndFileManage::encryptFile(const long &fileListId)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    QByteArray data = (fileNode.fileName + fileNode.fatherType + fileNode.childType + fileNode.createTime).toUtf8();
    QByteArray hash = QCryptographicHash::hash(data, QCryptographicHash::Sha256);
    QString hashStr = hash.toHex();
    // 在FileList中，根据fileListId更新havePassword和LastModifyTime
    fileNode.havePassword = hashStr;
    fileNode.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    daoError = qx::dao::update(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
        return false;
    }
    return true;
}

// 解密文件
bool ProjectAndFileManage::decryptFile(const long &fileListId)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    // 在FileList中，根据fileListId更新havePassword和LastModifyTime
    fileNode.havePassword = "";
    fileNode.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    daoError = qx::dao::update(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
        return false;
    }
    return true;
}

// 语法检查
bool ProjectAndFileManage::syntaxCheck(const QString &filePath)
{

    return true;
}

// 弹出一个对话框，允许用户选择要打印的内容(变量表、模块变量和编程文件),使用系统自带的打印对话框`QPrintDialog`
Q_INVOKABLE bool ProjectAndFileManage::printFile(const QString &jsonString)
{
    // 解析 JSON 字符串
    QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!jsonDoc.isArray())
    {
        QMessageBox::warning(nullptr, "打印错误", "传入的 JSON 格式不正确，应为数组。");
        return false;
    }

    QJsonArray jsonArray = jsonDoc.array();
    if (jsonArray.isEmpty())
    {
        QMessageBox::warning(nullptr, "打印错误", "传入的文件列表为空。");
        return false;
    }

    QPrinter printer;
    QPrintDialog printDialog(&printer);
    printDialog.setWindowTitle("打印");

    if (printDialog.exec() != QDialog::Accepted)
    {
        return false;
    }

    QPainter painter(&printer);

    for (int i = 0; i < jsonArray.size(); ++i)
    {
        QJsonObject fileObject = jsonArray.at(i).toObject();
        QString type = fileObject.value("type").toString();
        QString content = fileObject.value("content").toString();

        if (type == "image")
        {
            QByteArray imageData = QByteArray::fromBase64(content.toUtf8());
            QImage image;
            if (!image.loadFromData(imageData))
            {
                QMessageBox::warning(nullptr, "打印错误", QString("无法加载第 %1 个图片数据。").arg(i + 1));
                continue;
            }

            // 绘制图片
            QRect rect = painter.viewport();
            QSize size = image.size();
            size.scale(rect.size(), Qt::KeepAspectRatio);
            painter.setViewport(rect.x(), rect.y(), size.width(), size.height());
            painter.setWindow(image.rect());
            painter.drawImage(0, 0, image);
        }
        else if (type == "code")
        {
            QTextDocument textDoc;
            textDoc.setPlainText(content);
            textDoc.drawContents(&painter);
        }
        else
        {
            QMessageBox::warning(nullptr, "打印错误", QString("无法识别的第 %1 个内容类型：%2。").arg(i + 1).arg(type));
            continue;
        }

        // 如果不是最后一个页面，添加新的一页
        if (i < jsonArray.size() - 1)
        {
            printer.newPage();
        }
    }

    painter.end();
    return true;
}

Q_INVOKABLE bool ProjectAndFileManage::printPreviewFile(const QString &jsonString)
{
    // 解析 JSON 字符串
    QJsonDocument jsonDoc = QJsonDocument::fromJson(jsonString.toUtf8());
    if (!jsonDoc.isArray())
    {
        QMessageBox::warning(nullptr, "预览错误", "传入的 JSON 格式不正确，应为数组。");
        return false;
    }

    QJsonArray jsonArray = jsonDoc.array();
    if (jsonArray.isEmpty())
    {
        QMessageBox::warning(nullptr, "预览错误", "传入的文件列表为空。");
        return false;
    }

    QPrinter printer;
    printer.setResolution(900); // 设置高分辨率
    QPrintPreviewDialog previewDialog(&printer);
    previewDialog.setWindowTitle("打印预览");

    connect(&previewDialog, &QPrintPreviewDialog::paintRequested, [&](QPrinter * printer)
    {
        QPainter painter(printer);

        for (int i = 0; i < jsonArray.size(); ++i)
        {
            QJsonObject fileObject = jsonArray.at(i).toObject();
            QString type = fileObject.value("type").toString();
            QString content = fileObject.value("content").toString();

            if (type == "image")
            {
                QByteArray imageData = QByteArray::fromBase64(content.toUtf8());
                QImage image;
                if (!image.loadFromData(imageData))
                {
                    QMessageBox::warning(nullptr, "预览错误", QString("无法加载第 %1 个图片数据。").arg(i + 1));
                    continue;
                }

                // 计算图片绘制区域，保持原比例
                QRectF pageRect = QRectF(0, 0, printer->pageRect(QPrinter::DevicePixel).width(),
                                         printer->pageRect(QPrinter::DevicePixel).height());
                QImage scaledImage = image.scaled(pageRect.size().toSize(), Qt::KeepAspectRatio, Qt::SmoothTransformation);
                QRectF targetRect = QRectF((pageRect.width() - scaledImage.width()) / 2, (pageRect.height() - scaledImage.height()) / 2,
                                           scaledImage.width(), scaledImage.height());

                // 绘制图片并保持比例和清晰度
                painter.drawImage(targetRect, scaledImage);

            }
            else if (type == "code")
            {
                QTextDocument textDoc;
                textDoc.setPlainText(content);
                textDoc.drawContents(&painter);

            }
            else
            {
                QMessageBox::warning(nullptr, "预览错误", QString("无法识别的第 %1 个内容类型：%2。").arg(i + 1).arg(type));
                continue;
            }

            // 如果不是最后一个页面，添加新的一页
            if (i < jsonArray.size() - 1)
            {
                printer->newPage();
            }
        }

        painter.end();
    });
    connect(&previewDialog, &QPrintPreviewDialog::paintRequested, [&](QPrinter * printer)
    {
        QPainter painter(printer);

        for (int i = 0; i < jsonArray.size(); ++i)
        {
            QJsonObject fileObject = jsonArray.at(i).toObject();
            QString type = fileObject.value("type").toString();
            QString content = fileObject.value("content").toString();

            if (type == "image")
            {
                QByteArray imageData = QByteArray::fromBase64(content.toUtf8());
                QImage image;
                if (!image.loadFromData(imageData))
                {
                    QMessageBox::warning(nullptr, "预览错误", QString("无法加载第 %1 个图片数据。").arg(i + 1));
                    continue;
                }

                // 计算图片绘制区域，保持原比例
                QRectF pageRect = QRectF(0, 0, printer->pageRect(QPrinter::DevicePixel).width(),
                                         printer->pageRect(QPrinter::DevicePixel).height());
                QImage scaledImage = image.scaled(pageRect.size().toSize(), Qt::KeepAspectRatio, Qt::SmoothTransformation);
                QRectF targetRect = QRectF((pageRect.width() - scaledImage.width()) / 2, (pageRect.height() - scaledImage.height()) / 2,
                                           scaledImage.width(), scaledImage.height());

                // 绘制图片并保持比例和清晰度
                painter.drawImage(targetRect, scaledImage);

            }
            else if (type == "code")
            {
                QTextDocument textDoc;
                textDoc.setPlainText(content);
                textDoc.drawContents(&painter);

            }
            else
            {
                QMessageBox::warning(nullptr, "预览错误", QString("无法识别的第 %1 个内容类型：%2。").arg(i + 1).arg(type));
                continue;
            }

            // 如果不是最后一个页面，添加新的一页
            if (i < jsonArray.size() - 1)
            {
                printer->newPage();
            }
        }

        painter.end();
    });
    previewDialog.exec();
    return true;
}

// 打印配置
Q_INVOKABLE bool ProjectAndFileManage::printConfigFile()
{
    // 创建 QPrinter 对象
    QPrinter printer;

    // 创建 QPageSetupDialog 对象并与打印机对象关联
    QPageSetupDialog pageSetupDialog(&printer);
    pageSetupDialog.setWindowTitle("打印设置");

    // 显示系统自带的页面设置对话框，并判断用户是否接受设置
    if (pageSetupDialog.exec() == QDialog::Accepted)
    {
        // 用户已配置打印设置
        return true;
    }

    // 如果用户取消了对话框
    return false;
}

// 返回数据库文件列表
QJsonArray ProjectAndFileManage::getFileList()
{
    qx_query query;
    QList<QSharedPointer<FileList>> fileList;
    QSqlError daoError = qx::dao::fetch_all(fileList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all FileList :" + daoError.text().toStdString());
    }
    QJsonArray jsonArray;
    for (int i = 0; i < fileList.size(); i++)
    {
        QJsonObject jsonObject;
        jsonObject.insert("id", QString::number(fileList.at(i)->id));
        jsonObject.insert("fileName", fileList.at(i)->fileName);
        jsonObject.insert("fatherType", fileList.at(i)->fatherType);
        jsonObject.insert("childType", fileList.at(i)->childType);
        jsonObject.insert("fileIcon", fileList.at(i)->fileIcon);
        jsonObject.insert("projectTreeNodeId", QString::number(fileList.at(i)->projectTreeNodeId));
        jsonObject.insert("projectTreeParentNodeId", QString::number(fileList.at(i)->projectTreeParentNodeId));
        jsonObject.insert("createTime", fileList.at(i)->createTime);
        jsonObject.insert("lastModifyTime", fileList.at(i)->lastModifyTime);
        jsonObject.insert("havePassword", fileList.at(i)->havePassword);
        jsonObject.insert("dataType", fileList.at(i)->dataType);
        jsonObject.insert("settingName", fileList.at(i)->settingName);
        jsonObject.insert("executionOrder", fileList.at(i)->executionOrder);
        jsonObject.insert("executionType", fileList.at(i)->executionType);
        jsonObject.insert("absolutePath", fileList.at(i)->absolutePath);
        jsonArray.append(jsonObject);
    }
    // qInfo() << "jsonArray : " << jsonArray;
    return jsonArray;
}

QList<QSharedPointer<FileList>> ProjectAndFileManage::getFileListByFatherType(QString settingName, QString fatherType)
{
    qx_query query;
    query.where("settingName").isEqualTo(QVariant::fromValue(settingName)).and_("fatherType").isEqualTo(QVariant::fromValue(
                                                                                                                    fatherType));
    QList<QSharedPointer<FileList>> fileList;
    QSqlError daoError = qx::dao::fetch_by_query(query, fileList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all By FatherType FileList :" + daoError.text().toStdString());
    }

    return fileList;
}


QList<FileList> ProjectAndFileManage::getFileListByChildType(QString settingName, QString childType)
{
    qx_query query;
    query.where("settingName").isEqualTo(QVariant::fromValue(settingName)).and_("childType").isEqualTo(QVariant::fromValue(
                                                                                                                   childType));
    QList<FileList> fileList;
    QSqlError daoError = qx::dao::fetch_by_query(query, fileList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all By FatherType FileList :" + daoError.text().toStdString());
    }

    return fileList;
}

QList<FileList> ProjectAndFileManage::getFileListBySettingName(QString settingName)
{
    qx_query query;
    query.where("settingName").isEqualTo(QVariant::fromValue(settingName));
    QList<FileList> fileList;
    QSqlError daoError = qx::dao::fetch_by_query(query, fileList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all By FatherType FileList :" + daoError.text().toStdString());
    }

    return fileList;
}
QJsonObject ProjectAndFileManage::getFileListFromId(const long &fileListId)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_id FileList :" + daoError.text().toStdString());
        return QJsonObject();
    }
    return fileNode.ToJson();
}

// 根据FBReferenceList的owned获取对应数据
QVector<QStringList> ProjectAndFileManage::getFBReferenceListType(QVector<QStringList> &fbRefList)
{
    QVector<QStringList> vec;
    for (auto dy : fbRefList)
    {
        QString fileName = dy.at(0);
        QString settingName = dy.at(3);
        qx_query query;
        FileList fileNode;
        query.where("settingName").isEqualTo(QVariant::fromValue(settingName)).and_("fileName").isEqualTo(QVariant::fromValue(fileName));
        QSqlError daoError = qx::dao::fetch_by_query(query, fileNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
            return QVector<QStringList>();
        }
        QStringList list;
        list << fileName << dy.at(1) << dy.at(2) << fileNode.childType << settingName;
        vec.append(list);
    }
    return vec;
}

// 初始化
void ProjectAndFileManage::templateInit(QSqlDatabase &db)
{
    ProjectTreeTemplateManage::instance().init(db);
}

// 初始化数据库表
void ProjectAndFileManage::templateDatabaseTableInit()
{
    ProjectTreeTemplateManage::instance().databaseTableInit();
}

// 添加项目节点
void ProjectAndFileManage::addProjectNodeToTemplate()
{
    ProjectTreeTemplateManage::instance().AddProjectNode();
}

// 添加设备节点
void ProjectAndFileManage::addDeviceNodeToTemplate()
{
    ProjectTreeTemplateManage::instance().AddDeviceNode();
}

// 读取RecentProject1-RecentProject10
QJsonArray ProjectAndFileManage::readRecentProject()
{
    return RecentProject::instance().readRecentProject();
}

bool ProjectAndFileManage::appendRecentProject(const QString &Icon, const QString &ProjectName, const QString ProjectPath)
{
    return RecentProject::instance().appendRecentProject(Icon, ProjectName, ProjectPath);
}

bool ProjectAndFileManage::deleteRecentProject(const QString &ProjectPath)
{
    return RecentProject::instance().deleteRecentProject(ProjectPath);
}

bool ProjectAndFileManage::refreshRecentProject(const QString &projectPath)
{
    return RecentProject::instance().refreshRecentProject(projectPath);
}

bool ProjectAndFileManage::clearRecentProject()
{
    return RecentProject::instance().clearRecentProject();
}

// 初始化HardwareConfig模板
void ProjectAndFileManage::getHardwareConfigTemplate()
{
    // 记录HardwareConfig表中的节点直到SettingName不为空的节点停止记录
    m_hardwareConfigTemplate.clear();
    QList<QSharedPointer<HardwareConfig>> templateList;
    QSqlError daoError = qx::dao::fetch_all(templateList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all HardwareConfig :" + daoError.text().toStdString());
    }
    // LOG_INFO_DEFAULT("templateList.size() : " + QString::number(templateList.size()).toStdString());
    for (int i = 0; i < templateList.size(); i++)
    {
        if (!templateList.at(i)->settingName.isEmpty())
        {
            break;
        }
        m_hardwareConfigTemplate.append(templateList.at(i));
    }
}

// 从数据库中读取当前设备循环任务和执行顺序
void ProjectAndFileManage::readData(const QString &deviceName)
{
    // LOG_INFO_DEFAULT("deviceName : " + deviceName.toStdString());
    // 从hardwareConfig表中读取当前设备循环任务写入m_tasks
    m_tasks.clear();
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QList<QSharedPointer<HardwareConfig>> hardwareConfigList;
    QSqlError daoError = qx::dao::fetch_by_query(query, hardwareConfigList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query HardwareConfig :" + daoError.text().toStdString());
    }
    m_debugInte.ip = hardwareConfigList.at(0)->value;
    m_debugInte.port = hardwareConfigList.at(1)->value;
    // LOG_INFO_DEFAULT(hardwareConfigList.at(0)->value.toStdString());
    // LOG_INFO_DEFAULT(hardwareConfigList.at(1)->value.toStdString());

    for (int i = 2; i < hardwareConfigList.size(); i++)
    {
        m_tasks.append(TaskData(hardwareConfigList.at(i)->code, hardwareConfigList.at(i)->value));
    }

    m_orders.clear();
    query.clear();
    // 从数据库中按照ExecutionOrder字段升序读取
    query.where("settingName").isEqualTo(deviceName).and_("childType").isEqualTo("PROGRAM").orderAsc("ExecutionOrder");
    QList<QSharedPointer<FileList>> fileList;
    daoError = qx::dao::fetch_by_query(query, fileList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
    }
    // LOG_INFO_DEFAULT("readData");

    // 后期如果增加新的类型添加进fatherTypeList中
    QStringList fatherTypeList = { "CFC", "FBD", "IL", "LD", "SFC", "ST", "C", "PY" };
    // 从fileList中读取fatherType为fatherTypeList中的文件名存入m_fileNames
    for (int i = 0; i < fileList.size(); i++)
    {
        if (fatherTypeList.contains(fileList.at(i)->fatherType))
        {
            m_orders.append(ExecutionOrder(fileList.at(i)->fileName, fileList.at(i)->executionOrder, fileList.at(i)->executionType,
                                           fileList.at(i)->absolutePath));
        }
    }
    //
}

// CyclicTasksData只有修改数值
void ProjectAndFileManage::modifyTaskData(const QString &deviceName, const QString &name, const QString &newValue)
{
    qx_query query;
    // 写入hardwareConfig表
    // LOG_DEBUG_DEFAULT("deviceName : " + deviceName.toStdString(), "name : " + name.toStdString(),
    //                   "newValue : " + newValue.toStdString());
    query.where("settingName").isEqualTo(deviceName).and_("code").isEqualTo(name);
    HardwareConfig hardwareConfigNode;
    QSqlError daoError = qx::dao::fetch_by_query(query, hardwareConfigNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query HardwareConfig :" + daoError.text().toStdString());
    }
    hardwareConfigNode.value = newValue;
    daoError = qx::dao::update(hardwareConfigNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update HardwareConfig :" + daoError.text().toStdString());
    }
}

void ProjectAndFileManage::modifyOrderData(const QString &deviceName, const QJsonArray orderArr)
{
    // 写入fileList表
    for (auto it : orderArr)
    {
        QJsonObject obj = it.toObject();
        QString fileName = obj["name"].toString();
        int order = obj["order"].toInt();
        QString type = obj["type"].toString();
        qx_query query;
        query.where("settingName").isEqualTo(deviceName).and_("fileName").isEqualTo(fileName);
        FileList fileNode;
        QSqlError daoError = qx::dao::fetch_by_query(query, fileNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        }
        fileNode.executionOrder = order;
        fileNode.executionType = type;
        daoError = qx::dao::update(fileNode, &m_db);
        if (daoError.type() != QSqlError::NoError)
        {
            LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
        }
    }
}

void ProjectAndFileManage::modifyDebuggingInterfaceData(const QString &deviceName, const QString &ip, const QString &port)
{
    QList<QSharedPointer<HardwareConfig>> hardwareConfigList;
    // 获取hardwareConfig表中所有settingName为projectTreeNode.deviceName的节点
    qx_query query;
    query.where("settingName").isEqualTo(deviceName);
    QSqlError daoError = qx::dao::fetch_by_query(query, hardwareConfigList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_all HardwareConfig :" + daoError.text().toStdString());
    }
    hardwareConfigList.at(0)->value = ip;
    hardwareConfigList.at(1)->value = port;
    daoError = qx::dao::update(hardwareConfigList, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update_all HardwareConfig :" + daoError.text().toStdString());
    }
}

QJsonArray ProjectAndFileManage::getCyclicTasksData(const QString &deviceName)
{
    // LOG_INFO_DEFAULT("getCyclicTasksData");
    // qInfo() << __FILE__ << __func__ << __LINE__ << "deviceName : " << deviceName;
    readData(deviceName);
    QJsonArray jsonArray;
    for (int i = 0; i < m_tasks.size(); i++)
    {
        QJsonObject jsonObject;
        jsonObject.insert("name", m_tasks.at(i).code);
        jsonObject.insert("value", m_tasks.at(i).value);
        jsonArray.append(jsonObject);
    }
    // LOG_INFO_DEFAULT("getCyclicTasksData");

    return jsonArray;
}

// 添加文件后需要再次调用该函数刷新列表
QJsonArray ProjectAndFileManage::getExecutionOrderData(const QString &deviceName)
{
    // LOG_INFO_DEFAULT("getExecutionOrderData");
    // qInfo() << __FILE__ << __func__ << __LINE__ << "deviceName : " << deviceName;
    readData(deviceName);

    // qInfo() << m_orders;
    QJsonArray jsonArray;
    for (int i = 0; i < m_orders.size(); i++)
    {
        QJsonObject jsonObject;
        jsonObject.insert("name", m_orders.at(i).fileName);
        jsonObject.insert("order", m_orders.at(i).executionOrder);
        jsonObject.insert("type", m_orders.at(i).executionType);
        jsonObject.insert("path", m_orders.at(i).absolutePath);
        jsonArray.append(jsonObject);
    }
    // LOG_INFO_DEFAULT("getExecutionOrderData");

    return jsonArray;
}

QJsonObject ProjectAndFileManage::getDebuggingInterfaceData(const QString &deviceName)
{
    // LOG_INFO_DEFAULT("getDebuggingInterfaceData");
    readData(deviceName);
    QJsonObject jsonObject;
    jsonObject.insert("ip", m_debugInte.ip);
    jsonObject.insert("port", m_debugInte.port);
    // LOG_INFO_DEFAULT("getDebuggingInterfaceData");
    return jsonObject;
}

QString ProjectAndFileManage::hardwareConfigToXml(const QString &deviceName)
{
    // 读取数据
    readData(deviceName);
    QString xmlData;
    // 写入xml
    QXmlStreamWriter xmlWriter(&xmlData);
    xmlWriter.setCodec("UTF-8");
    xmlWriter.setAutoFormatting(true);
    xmlWriter.writeStartDocument("1.0", true); // second parameter is for standalone declaration
    xmlWriter.writeStartElement("HWConfig");

    xmlWriter.writeAttribute("Name", "SIM1");
    // 在数据库中添加HardwareModule的字段
    xmlWriter.writeAttribute("HardwareModule", "SmartSIM");
    xmlWriter.writeStartElement("Options");
    xmlWriter.writeTextElement("Upload", "0");
    xmlWriter.writeTextElement("IncludeLib", "0");
    xmlWriter.writeTextElement("SymbolTable", "1");
    xmlWriter.writeTextElement("Optimization", "1");
    xmlWriter.writeTextElement("EnableTSCodeGen", "1");
    xmlWriter.writeEndElement(); // End of Options
    // 写入循环任务
    xmlWriter.writeStartElement("Tasks");
    for (int i = 0; i < m_tasks.size(); i++)
    {
        xmlWriter.writeStartElement(QString("T%1").arg(i));
        xmlWriter.writeAttribute("Enabled", "1");
        xmlWriter.writeCharacters(m_tasks.at(i).value);
        xmlWriter.writeEndElement();
    }
    for (int i = 1; i <= 8; i++)
    {
        xmlWriter.writeStartElement(QString("I%1").arg(i));
        xmlWriter.writeAttribute("Source", "0");
        xmlWriter.writeCharacters("1");
        xmlWriter.writeEndElement();
    }
    xmlWriter.writeEndElement();

    xmlWriter.writeStartElement("Multiprocessing");
    xmlWriter.writeTextElement("Master_CPU_in_Rack", "1");
    xmlWriter.writeEndElement();

    // 写入端口号 ip
    xmlWriter.writeStartElement("Connection");
    xmlWriter.writeAttribute("Name", "XJ_TCP");
    xmlWriter.writeAttribute("Driver", "TCP52");
    xmlWriter.writeTextElement("Port", m_debugInte.port);
    xmlWriter.writeTextElement("IP", m_debugInte.ip);
    xmlWriter.writeTextElement("Motorola", "0");
    xmlWriter.writeTextElement("CommProxy", "0");
    xmlWriter.writeTextElement("MasterCPU", "0");
    xmlWriter.writeTextElement("SlotNumber", "0");
    xmlWriter.writeEndElement(); // End of Connection
    // 写入ProgramExecution
    xmlWriter.writeStartElement("ProgramExecution");
    for (int i = 1; i <= 8; i++)
    {
        xmlWriter.writeStartElement(QString("I%1").arg(i));
        xmlWriter.writeAttribute("count", "0");
        xmlWriter.writeEndElement();
    }

    for (int i = 1; i <= 5; i++)
    {
        xmlWriter.writeStartElement(QString("T%1").arg(i));
        // 查找文件类型为QString("T%1").arg(i)的文件个数
        int count = 0;
        for (int j = 0; j < m_orders.size(); j++)
        {
            if (m_orders.at(j).executionType == QString("T%1").arg(i))
            {
                count++;
            }
        }
        xmlWriter.writeAttribute("count", QString::number(count));
        if (count != 0)
        {
            for (int j = 0; j < m_orders.size(); j++)
            {
                if (m_orders.at(j).executionType == QString("T%1").arg(i))
                {
                    QFileInfo fileInfo(m_orders.at(j).fileName);
                    xmlWriter.writeTextElement(QString("Program%1").arg(j + 1), fileInfo.baseName());
                }
            }
        }
        xmlWriter.writeEndElement();
    }

    xmlWriter.writeEndElement(); // End of ProgramExecution
    xmlWriter.writeEndElement(); // End of HWConfig
    xmlWriter.writeEndDocument();

    QDomDocument xmlDoc;
    xmlDoc.setContent(xmlData);
    return xmlDoc.toString();
}

// destFilePath: file///F:/example"
QString ProjectAndFileManage::zip(const QString &zipPath, const QString &password)
{
    QString currentProjectPath = getCurrentProjectPath();
    // 打包的路径不能是当前项目路径的子路径
    if (zipPath.startsWith(currentProjectPath))
    {
        //"C:/Users/<USER>/Desktop/1" "C:/Users/<USER>/Desktop"
        // qInfo() << currentProjectPath << zipPath;
        LOG_ERROR_DEFAULT("打包路径有误");
        return QString::fromUtf8("打包路径有误");
    }
    QFileInfo fileInfo(currentProjectPath);
    QString projectName = fileInfo.baseName();
    QString zipPathTemp = zipPath;
    handleString(zipPathTemp);
    zipPathTemp = zipPathTemp + QDir::separator() + projectName + ".pak";
    // qInfo() << "zipPathTemp: " << zipPathTemp << "getCurrentProjectPath():" << getCurrentProjectPath() << "password:" << password;
    // 判断是否有同名文件
    if (QFileInfo(zipPathTemp).exists())
    {
        LOG_ERROR_DEFAULT("存在同名文件");
        return QString::fromUtf8("存在同名文件");
    }
    if (!QZip::instance().zip(zipPathTemp, getCurrentProjectPath(), password))
    {
        return QString::fromUtf8("压缩过程未知错误");
    }
    return QString();
}

// 同名文件强制压缩
bool ProjectAndFileManage::forceZip(const QString &zipPath, const QString &password)
{
    QString currentProjectPath = getCurrentProjectPath();
    QFileInfo fileInfo(currentProjectPath);
    QString projectName = fileInfo.baseName();
    QString zipPathTemp = zipPath;
    handleString(zipPathTemp);
    zipPathTemp = zipPathTemp + QDir::separator() + projectName + ".pak";
    return QZip::instance().zip(zipPathTemp, getCurrentProjectPath(), password);
}

QString getUniqueDirectoryName(const QString &basePath, const QString &dirName)
{
    // 检查原始目录名是否存在
    QFileInfo fileInfo(basePath + QDir::separator() + dirName);
    // qInfo() << __LINE__ << " fileInfo " << fileInfo; // C:\Users\<USER>\Desktop\11.pak
    if (!fileInfo.exists())
    {
        // 如果不存在，直接返回原始目录名
        return dirName;
    }

    // 如果存在，开始添加编号以生成唯一名称
    int index = 1;
    QString newDirName;
    do
    {
        // 组装新的目录名，格式为“原始名称(编号)”
        newDirName = QString("%1(%2)").arg(dirName).arg(index);
        fileInfo.setFile(basePath + QDir::separator() + newDirName);
        index++; // 增加编号以便下次迭代
    } while (fileInfo.exists()); // 检查新名称是否已存在，直到找到一个不存在的名称

    return newDirName; // 返回生成的唯一目录名
}
//  "C:/Users/<USER>/Desktop/11\\1\\User\\Core1\\22.FBD";
//  "/11";
//  return "\1\User\Core1\22.FBD"
QString extractSubstring(const QString &input, const QString &delimiter)
{
    // 将分隔符中可能的特殊字符转义
    QString escapedDelimiter = QRegularExpression::escape(delimiter);
    QRegularExpression re(escapedDelimiter + ".*");

    QRegularExpressionMatch match = re.match(input);
    if (match.hasMatch())
    {
        QString matched = match.captured(0);
        // qInfo() << __LINE__ << "input" << input << "delimiter" << delimiter;
        return matched.mid(delimiter.length());
    }
    // qInfo() << __LINE__ << "input" << input << "delimiter" << delimiter;
    return QString();
}

void ProjectAndFileManage::modifyProjectPath(const QString &filePath)
{
    QFile file(filePath);
    if (!file.open(QIODevice::ReadOnly))
    {
        LOG_ERROR_DEFAULT("Could not open file for reading:");
        return;
    }

    QByteArray fileData = file.readAll();
    file.close();

    QJsonParseError jsonError;
    QJsonDocument document = QJsonDocument::fromJson(fileData, &jsonError);
    if (jsonError.error != QJsonParseError::NoError)
    {
        LOG_ERROR_DEFAULT("JSON parse error:" + jsonError.errorString().toStdString());
        return;
    }

    QJsonObject jsonObject = document.object();
    jsonObject["ProjectPath"] = filePath;

    QJsonDocument newDocument(jsonObject);

    if (!file.open(QIODevice::WriteOnly))
    {
        LOG_ERROR_DEFAULT("Could not open file for writing:" + filePath.toStdString());
        return;
    }

    file.write(newDocument.toJson(QJsonDocument::Indented));
    file.close();
}

int ProjectAndFileManage::getMaxExecutionOrderFromTask(const QString &deviceName, const QString &taskName)
{
    int num = 0;
    QList<FileList> files;
    qx_query query;
    query.where("settingName").isEqualTo(deviceName).and_("ExecutionType").isEqualTo(taskName).orderDesc("ExecutionOrder");
    QSqlError daoError = qx::dao::fetch_by_query(query, files, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("fetch_by_query getMaxExecutionOrderFromTask :" + daoError.text().toStdString());
    }
    if (files.size() > 0)
    {
        num = files[0].executionOrder;
    }
    return num;
}

bool ProjectAndFileManage::encryption(const QString &path, const QByteArray &key)
{
    if (key.isEmpty()) {
        qWarning() << "Key is empty!";
        return false;
    }

    QByteArray randomPrefix = generateRandomByteArray(129);
    QString tempTarget = path + ".temp_encrypted";

    QFile inputFile(path);
    if (!inputFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open input file for reading:" << path;
        return false;
    }

    QByteArray fileData = inputFile.readAll();
    inputFile.close();
    QByteArray encryptedData;
    encryptedData.append(randomPrefix);

    quint8 start = randomPrefix.at(88) % key.size();

    for (int i = 0; i < fileData.size(); ++i) {
        int keyIndex = (start + i) % key.size();
        encryptedData.append(fileData[i] ^ key[keyIndex]);
    }
    QFile outputFile(tempTarget);
    if (!outputFile.open(QIODevice::WriteOnly)) {
        qDebug() << "Failed to open output file for writing:" << tempTarget;
        return false;
    }

    outputFile.write(encryptedData);
    outputFile.close();

    // 删除原文件
    if (!QFile::remove(path)) {
        qDebug() << "Failed to remove the original file:" << path;
        QFile::remove(tempTarget);
        return false;
    }

    // 将临时文件重命名为原文件名，从而实现替换
    if (!QFile::rename(tempTarget, path)) {
        qDebug() << "Failed to rename the encrypted file to replace the original file:" << path;
        QFile::remove(tempTarget);
    }

    qDebug() << "Encryption completed successfully and original file replaced.";
    return true;
}

bool ProjectAndFileManage::encryption(const QByteArray& bytes, const QString& path, const QByteArray& key)
{
    if (key.isEmpty()) {
        qWarning() << "Key is empty!";
        return false;
    }

    QByteArray randomPrefix = generateRandomByteArray(129);
    QString tempTarget = path + ".temp_encrypted";

    QByteArray encryptedData;
    encryptedData.append(randomPrefix);

    quint8 start = randomPrefix.at(88) % key.size();

    for (int i = 0; i < bytes.size(); ++i) {
        int keyIndex = (start + i) % key.size();
        encryptedData.append(bytes[i] ^ key[keyIndex]);
    }
    QFile outputFile(tempTarget);
    if (!outputFile.open(QIODevice::WriteOnly)) {
        qDebug() << "Failed to open output file for writing:" << tempTarget;
        return false;
    }

    outputFile.write(encryptedData);
    outputFile.close();

    // 删除原文件
    if (!QFile::remove(path)) {
        qDebug() << "Failed to remove the original file:" << path;
        QFile::remove(tempTarget);
        return false;
    }

    // 将临时文件重命名为原文件名，从而实现替换
    if (!QFile::rename(tempTarget, path)) {
        qDebug() << "Failed to rename the encrypted file to replace the original file:" << path;
        QFile::remove(tempTarget);
    }

    qDebug() << "Encryption completed successfully and original file replaced.";
    return true;
}

bool ProjectAndFileManage::decryption(const QByteArray &encryptedFile, const QByteArray &key, QByteArray &decryptedData)
{
    // 检查文件长度是否足够
    if (encryptedFile.size() < 129) { // 假设头部长度是129字节
        qWarning() << "Encrypted file is too short!";
        return false;
    }

    // 分离头部和加密数据
    QByteArray head = encryptedFile.left(129);
    QByteArray encryptedData = encryptedFile.mid(129);

    // 计算密钥起始位置
    quint8 start = head.at(88) % key.size();

    // 解密数据
    decryptedData.resize(encryptedData.size());
    for (int i = 0; i < encryptedData.size(); ++i) {
        int keyIndex = (start + i) % key.size();
        decryptedData[i] = encryptedData[i] ^ key[keyIndex];
    }
    return true;
}

QByteArray ProjectAndFileManage::generateRandomByteArray(int length)
{
    QByteArray randomByteArray(length, 0x00);
    for (int i = 0; i < length; ++i) {
        randomByteArray[i] = static_cast<char>(QRandomGenerator::global()->bounded(0, 256));
    }
    return randomByteArray;
}

// filePath: 解压的zip文件的路径。
// dirPath: 要解压的目录的路径。
QString ProjectAndFileManage::unzip(const QString &filePath, const QString &dirPath, const QString &password)
{
    QString filePathTemp = filePath;
    QString dirPathTemp = dirPath;

    handleString(filePathTemp);
    QFileInfo fileInfo(filePath);

    // qInfo() << __LINE__ << " filePathTemp " << filePathTemp; // C:/Users/<USER>/Desktop/11.pak
    QString fileName = QFileInfo(filePathTemp).baseName();
    // qInfo() << __LINE__ << " fileName " << fileName; //"11"

    // qInfo() << __LINE__ << " dirPathTemp " << dirPathTemp;
    QString newfileName = getUniqueDirectoryName(dirPathTemp, fileName);
    // qInfo() << __LINE__ << " newfileName " << newfileName;

    // qInfo() << __LINE__ << " filePathTemp " << filePathTemp;
    // qInfo() << __LINE__ << " dirPathTemp " << dirPathTemp;
    // qInfo() << __LINE__ << " password " << password; //"C:/Users/<USER>/Desktop/11.pak"
    QString newDirPathTemp = dirPathTemp + QDir::separator() + newfileName;

    if ((QZip::instance().unzip(filePathTemp, newDirPathTemp, password)).isEmpty())
    {
        LOG_ERROR_DEFAULT("解压密码有误");
        return QString("解压密码有误");
    }

    QString projPath = newDirPathTemp + QDir::separator() + fileName + ".proj";
    // qInfo() << __LINE__ << " " << newDirPathTemp + QDir::separator() + fileName + ".proj";
    modifyProjectPath(projPath);
    openProject(projPath);

    QList<QSharedPointer<FileList>> filelist;
    QSqlError daoError = qx::dao::fetch_all(filelist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("读取文件列表出错" + daoError.text().toStdString());
    }
    // 修改每个文件的路径
    for (int i = 0; i < filelist.size(); i++)
    {
        QString input = filelist.at(i)->absolutePath;
        QString delimiter = "/" + fileName; // 项目名
        // qInfo() << __LINE__ << " input :" << input << " delimiter: " << delimiter;
        filelist.at(i)->absolutePath = newDirPathTemp + QDir::separator() + filelist.at(i)->settingName + QDir::separator() +
                                       getFilePathFromType(filelist.at(i)->childType, filelist.at(i)->fileName);
    }
    daoError = qx::dao::update(filelist, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update table filelist :" + daoError.text().toStdString());
    }

    closeProject();
    return QString();
}

bool ProjectAndFileManage::updateFile_DataType(const long& fileListId, const QString& dataType){
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    fileNode.dataType = dataType;
    fileNode.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    daoError = qx::dao::update(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
        return false;
    }
    return true;
}

bool ProjectAndFileManage::getFileByName(const QString &fileName, FileList &file)
{
    QList<FileList> files;
    qx_query query;
    query.where("settingName").isEqualTo(getCurrentDeviceName()).and_("fileName").isEqualTo(fileName).orderDesc("ExecutionOrder");
    QSqlError daoError = qx::dao::fetch_by_query(query, files, &m_db);
    if(files.size() > 0){
        file = files[0];
    }else{
        return false;
    }
    return true;
}

QJsonObject ProjectAndFileManage::getFileByName(const QString& fileName)
{
    QList<FileList> files;
    qx_query query;
    query.where("settingName").isEqualTo(getCurrentDeviceName()).and_("fileName").isEqualTo(fileName).orderDesc("ExecutionOrder");
    QSqlError daoError = qx::dao::fetch_by_query(query, files, &m_db);
    if(files.size() < 1)return QJsonObject();
    return files[0].ToJson();
}

bool ProjectAndFileManage::encryptFile(const long &fileListId, const QString& password)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return false;
    }
    fileNode.havePassword = password;
    fileNode.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    daoError = qx::dao::update(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
        return false;
    }

    // 文件加密
    QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "Failed to open file: " << keyPath;
        return false;
    }
    // 读取key
    QByteArray key = keyFile.readAll();
    keyFile.close();
    if(!encryption(fileNode.absolutePath, key)){
        qDebug() << "Failed to encryption, fileName: " << fileNode.fileName;
        return false;
    }
    return true;
}

int ProjectAndFileManage::decryptFile(const long &fileListId, const QString &password)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return -5;
    }
    // 判断密码是否正确
    if(fileNode.havePassword != password){
        qDebug() << "erro password";
        return -1;
    }
    // 读取key
    QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
    QFile keyFile(keyPath);
    if (!keyFile.open(QIODevice::ReadOnly)) {
        qDebug() << "loadEncryptedFile func, Failed to open file: " << keyPath;
        return -2;
    }
    QByteArray key = keyFile.readAll();
    keyFile.close();
    // 读取文件
    QFile sourceFile(fileNode.absolutePath);
    if(!sourceFile.open(QIODevice::ReadOnly)) {
        qDebug() << "loadEncryptedFile func, Failed to open file: " << keyPath;
        return -3;
    }
    QByteArray source = sourceFile.readAll();
    sourceFile.close();
    QByteArray bytes;
    if(!decryption(source, key, bytes)){
        qDebug() << "loadEncryptedFile func, Failed to decryption";
        return -4;
    }

    QFile outputFile(fileNode.absolutePath);
    if (!outputFile.open(QIODevice::WriteOnly)) {
        qDebug() << "Failed to open output file for writing:" << fileNode.absolutePath;
        return -7;
    }
    qDebug() << bytes.toHex(' ');
    outputFile.write(bytes);
    outputFile.close();

    fileNode.havePassword = "";
    fileNode.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    daoError = qx::dao::update(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
        return -6;
    }
    return 1;
}

int ProjectAndFileManage::loadFile(const long &fileListId, const QString &password, QByteArray& bytes)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
    {
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
        return -5;
    }
    // 判断密码是否正确
    if(fileNode.havePassword != password){
        qDebug() << "erro password";
        return -1;
    }
    // 读取文件
    QFile sourceFile(fileNode.absolutePath);
    if(!sourceFile.open(QIODevice::ReadOnly)) {
        qDebug() << "loadEncryptedFile func, Failed to open file: " << fileNode.absolutePath;
        return -3;
    }
    // 非加密文件不需要解密
    if(password == ""){
        bytes = sourceFile.readAll();
    }
    // 加密文件解密读取
    else{
        // 读取key
        QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
        QFile keyFile(keyPath);
        if (!keyFile.open(QIODevice::ReadOnly)) {
            qDebug() << "loadEncryptedFile func, Failed to open file: " << keyPath;
            return -2;
        }
        QByteArray key = keyFile.readAll();
        keyFile.close();
        QByteArray source = sourceFile.readAll();
        if(!decryption(source, key, bytes)){
            qDebug() << "loadEncryptedFile func, Failed to decryption";
            return -4;
        }
    }
    sourceFile.close();
    return 1;
}

bool ProjectAndFileManage::isEncryptedFile(const long &fileListId)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());
    if(fileNode.havePassword.size() > 0)
        return true;
    return false;
}

bool ProjectAndFileManage::saveFile(const long &fileListId, QByteArray &bytes)
{
    FileList fileNode;
    fileNode.id = fileListId;
    QSqlError daoError = qx::dao::fetch_by_id(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError && fileNode.fileName.isEmpty())
        LOG_ERROR_DEFAULT("fetch_by_query FileList :" + daoError.text().toStdString());

    // 保存加密文件
    if(fileNode.havePassword.size() > 0){
        // 文件加密
        QString keyPath = QFileInfo(QCoreApplication::applicationFilePath()).absolutePath() + "\\Settings\\IEC_STANDARD_FUN_CFCFBD_BL.inc";
        QFile keyFile(keyPath);
        if (!keyFile.open(QIODevice::ReadOnly)) {
            qDebug() << "Failed to open file: " << keyPath;
            return false;
        }
        // 读取key
        QByteArray key = keyFile.readAll();
        keyFile.close();
        if(!encryption(bytes, fileNode.absolutePath, key)){
            qDebug() << "Failed to encryption, fileName: " << fileNode.fileName;
            return false;
        }
    }
    // 保存非加密文件
    else{
        QFile file(fileNode.absolutePath);
        if (!file.open(QIODevice::WriteOnly)) {
            qDebug() << "Failed to open file: " << fileNode.absolutePath;
            return false;
        }
        file.write(bytes);
        file.close();
    }



    fileNode.lastModifyTime = QDateTime::currentDateTime().toString("yyyy-MM-dd HH:mm:ss");
    daoError = qx::dao::update(fileNode, &m_db);
    if (daoError.type() != QSqlError::NoError)
    {
        LOG_ERROR_DEFAULT("update FileList :" + daoError.text().toStdString());
        return false;
    }
    return true;
}
