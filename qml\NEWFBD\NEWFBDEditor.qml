﻿
/******************************************************************************
 * Copyright 2023-2043 Three Gorges Intelligent Industrial Control Technology Co., Ltd.
 * All right reserved. See COPYRIGHT for detailed Information.
 *
 * @file       NEWFBDEditor.qml
 * @brief      新FBD编辑器主框架
 *
 * <AUTHOR>
 * @date       2024/08/12
 * @history
 *****************************************************************************/
import QtQuick 2.15
import QtQuick.Controls 2.15
import FluentUI 1.0
import "qrc:/qml/skin"
import "qrc:/qml/control/common"
import "qrc:/qml/control/menu"
import "qrc:/qml/gv"
import "qrc:/qml/variableshow"

//新FBD编辑器主框架
Item {
    id: control

    width: parent ? parent.width : 800
    height: parent ? parent.height : 600
    clip: true

    //对应文件键值
    property string fileKey: ""
    property string deviceName: ""

    property var multiControl

    //配置
    property var config: NEWFBDEditConfig
    //文件信息
    property var fileInfo
    //是否显示背景网格
    property bool showGird: false

    //页面宽度块数量
    property int widthNumber: 100
    //页面高度块数据
    property int heightNumber: 100
    //确定是在网络上部还是下部点击
    property bool rightClickTopOrBottom: true

    // linkTo标题
    property string linkToTitle: ""

    //任务配置框是否显示
    property bool tasksetShow: false

    //功能块列表名称
    property var blockfunctionList: []

    //当前网络
    property int currentNetworkNumber: 0

    //引脚连线标记位
    property bool isLinking: false
    //第一个选中引脚信息
    property var firstConnectorData

    property alias mainroot: root

    //刷新线条信号
    signal refreshLine

    // 包含查询的文本所在的块元件
    property var includeComs: []
    // 当前查询索引
    property int currentSearchIndex: 0

    // 连接全局快捷键信号对象
    Connections {
        target: shortcutController

        // 复制
        function onCopy(handler) {
            if (fileName === activeFile) {
                handler(multiShortcutHandler.copy())
            }
        }

        // 剪切
        function onShear(handler) {
            if (fileName === activeFile) {
                handler(multiShortcutHandler.shear())
            }
        }

        // 粘贴
        function onPaste(handler) {
            if (fileName === activeFile) {
                multiShortcutHandler.rightPaste(handler)
            }
        }

        // 撤销
        function onUndo(handler) {
            if (fileName === activeFile) {
                multiShortcutHandler.undo(handler)
            }
        }

        // 反撤销
        function onRedo(handler) {
            if (fileName === activeFile) {
                multiShortcutHandler.redo(handler)
            }
        }

        // 删除
        function onDel() {
            if (fileName === activeFile) {
                multiShortcutHandler.del()
            }
        }
    }

    FBDMultiShortcuts {
        id: multiShortcutHandler
    }

    //顶部选择悬浮框
    QkButtonRow {
        id: btnRect
        anchors.top: control.top
        anchors.topMargin: 1
        z: 10
        Row {
            anchors.verticalCenter: parent.verticalCenter
            spacing: 10

            Text {
                anchors.verticalCenter: parent.verticalCenter
                text: qsTr("Find and locate") + ":"
            }

            QkTextField {
                id: search_text
                onTextChanged: {
                    searchBlock(text.toLowerCase())
                }
            }

            Text {
                anchors.verticalCenter: parent.verticalCenter
                text: qsTr("Replace") + ":"
            }

            QkTextField {
                id: replace_text
            }

            Rectangle {
                anchors.verticalCenter: parent.verticalCenter
                width: 50
                height: 20
                visible: search_count.text
                Text {
                    id: search_count
                    anchors.centerIn: parent
                }
            }

            QkButton {
                anchors.verticalCenter: parent.verticalCenter
                width: 40
                height: 20
                Image {
                    width: 20
                    height: 20
                    anchors.centerIn: parent
                    source: "qrc:/assets/icon/slices/t_pageup.png"
                }

                onClicked: {
                    if (includeComs.length > 0) {
                        currentSearchIndex--

                        if (currentSearchIndex < 0) {
                            currentSearchIndex = includeComs.length - 1
                        }

                        search_count.text = (currentSearchIndex + 1) + "/" + includeComs.length

                        setSearchComs(search_text.text.toLowerCase())
                        searchBlock(search_text.text.toLowerCase())
                    }
                }
            }

            QkButton {
                anchors.verticalCenter: parent.verticalCenter
                width: 40
                height: 20
                Image {
                    width: 20
                    height: 20
                    anchors.centerIn: parent
                    source: "qrc:/assets/icon/slices/t_pagedown.png"
                }

                onClicked: {
                    if (includeComs.length > 0) {
                        currentSearchIndex++

                        if (currentSearchIndex > (includeComs.length - 1)) {
                            currentSearchIndex = 0
                        }

                        search_count.text = (currentSearchIndex + 1) + "/" + includeComs.length

                        setSearchComs(search_text.text.toLowerCase())
                        searchBlock(search_text.text.toLowerCase())
                    }
                }
            }

            QkButton {
                anchors.verticalCenter: parent.verticalCenter
                width: 100
                height: 20
                text: qsTr("Replace All")

                onClicked: {
                    if (search_text.text.length === 0
                            || replace_text.text.length === 0) {
                        return
                    }

                    if (includeComs.length === 0) {
                        // 没有查找到块元件
                        return
                    }

                    // 需要替换的文本
                    const replaceText = replace_text.text.toLowerCase()
                    // 根据文本查找变量
                    const variables = findVariable(replaceText)

                    if (variables.length === 0 || variables.length > 1) {
                        // 没有找到要替换的变量
                        // 或找到的变量名称有重复
                        return
                    }

                    const variable = variables[0]
                    // 根据文本内容查找的引脚变量组件
                    const pinVariables = findPinVariable(
                                           search_text.text.toLowerCase())

                    for (var pIndex = 0; pIndex < pinVariables.length; pIndex++) {
                        const pinComponent = pinVariables[pIndex]

                        if (pinComponent.ParentPinDataType === variable.dataType) {
                            fbdManage.modifyVariableComponent(
                                        fileKey, pinComponent.NetworkNumber,
                                        pinComponent.Number, variable.scope,
                                        variable.name, variable.owned)
                        }
                    }

                    getDataBind()
                }
            }

            QkButton {
                visible: fileInfo ? (fileInfo.Type === "PROGRAM"
                                     && fileInfo.Code === "CFC") : false
                text: "任务配置"
                onClicked: {
                    control.tasksetShow = !control.tasksetShow
                }
            }
            QkButton {
                id: btn_reset
                text: "重置画面"
                onClicked: {
                    config.currentScale = 1
                    root.x = 5
                    root.y = btnRect.height + 10
                }
            }
            QkButton {
                visible: false
                text: "Save"
                onClicked: {
                    //回环检查
                    if (fbdManage.checkCycle(control.fileKey)) {
                        console.log("有回环链接情况")
                    }
                    //检查输出漏项
                    if (fbdManage.checkBlockOutConnection(control.fileKey)) {
                        console.log("有引脚未链接变量")
                        messageDialog.show(
                                    "有功能块的引脚未链接变量，请检查保证输入输出引脚都有正确的链接变量！",
                                    null, null)
                    }
                    //保存当前文件
                    multiControl.saveCurrentFile()
                    //console.log("Save file:",fbdManage.saveFile(control.fileKey))
                    getDataBind()
                }
            }
        }
    }

    //检查信息通知栏
    MouseArea {
        anchors.fill: parent
        drag.target: root
        drag.axis: Drag.XAndYAxis
        drag.maximumX: 10000
        drag.minimumX: -10000
        drag.minimumY: -10000
        drag.maximumY: 10000
        acceptedButtons: Qt.LeftButton | Qt.RightButton
        onClicked: {
            if (mouse.button == Qt.LeftButton) {
                clearSelect()
            } else if (mouse.button == Qt.RightButton) {
                rightClickTopOrBottom = mouseY <= control.height / 2
                //右键菜单
                contentMenu.popup()
            }
        }
        onWheel: {
            if (wheel.angleDelta.y > 0) {
                config.currentScale += 0.1
                if (config.currentScale >= 2)
                    config.currentScale = 2
            } else {
                config.currentScale -= 0.1
                if (config.currentScale <= 0.2)
                    config.currentScale = 0.2
            }
        }
    }

    //画布窗口
    Rectangle {
        id: root
        x: 5
        y: btnRect.height + 10
        width: parent.width
        height: parent.height - btnRect.height
        border.width: 0
        border.color: "blue"
        antialiasing: true
        smooth: true
        scale: config.currentScale

        onScaleChanged: {
            clearSelect()
        }

        Column {
            id: column
            anchors {
                left: root.left
                leftMargin: 0
                top: root.top
                topMargin: 0
            }
            spacing: 5
            Repeater {
                id: pageRepeater
                model: listModel
                //网络
                NEWFBDNetwork {
                    id: netWork
                    netData: model
                    mainControl: control
                    networkNumber: model.Number
                    onSetCanvasPosition: control.setCanvasPosition(xPos,
                                                                   yPos, width,
                                                                   height,
                                                                   offsetHeight)
                }
            }
        }
    }

    ListModel {
        id: listModel
    }

    Connections {
        target: fbdManage
        function onFileChanged(filekey) {
            if (filekey === control.fileKey) {
                console.log("NEWFBDEditor getDataBind", filekey)
                getDataBind()
            }
        }

        function onNetworkChanged(filekey, networknumber, data) {
            if (filekey === control.fileKey) {
                console.log("NEWFBDNetwork onNetworkChanged", filekey,
                            networknumber, JSON.stringify(data))
                for (var l = 0; l < data.length; l++) {
                    if (data[l].action === "modify") {
                        for (var i = 0; i < listModel.count; i++) {
                            if (listModel.get(i).Number === networknumber) {
                                listModel.setProperty(i, data[l].name,
                                                      data[l].value)
                                break
                            }
                        }
                    }
                }
            } else {
                console.log("文件key不匹配")
            }
        }
    }

    QkPopupDialog {
        id: popupDialog
    }

    QkMenu {
        // 右键菜单
        // 添加页 删除页 拷贝或移动页（对话框） 显示/隐藏网格 设置墙宽度(对话框) CFC参数信息(对话框) 搜索查询框
        id: contentMenu
        width: 160
        QkMenuItem {
            text: qsTr("Add Network")
            onTriggered: {
                if (fbdManage.addNetwork(fileKey, 99999)) {
                    console.log("Add Network Ok")
                }
            }
        }
    }

    FluContentDialog {
        id: messageDialog

        property var okfunc
        property var nofunc

        title: qsTr("Tip") + (trans ? trans.transString : "")
        message: qsTr("Input Error") + (trans ? trans.transString : "")
        negativeText: "取消"
        positiveText: "确定"
        buttonFlags: FluContentDialogType.PositiveButton
        onPositiveClicked: {
            if (okfunc) {
                okfunc()
            }
        }
        onNegativeClicked: {
            if (nofunc) {
                nofunc()
            }
        }
        function show(caption, funcok, funcno, type = "info") {
            messageDialog.okfunc = funcok
            messageDialog.nofunc = funcno
            if (type === "info") {
                messageDialog.buttonFlags = FluContentDialogType.PositiveButton
            } else if (type === "confirm") {
                messageDialog.buttonFlags = FluContentDialogType.NegativeButton
                        | FluContentDialogType.PositiveButton
            }
            messageDialog.message = caption
            messageDialog.open()
        }
    }

    //绑定数据
    function getDataBind() {
        //获取CFC平台参数
        control.fileInfo = fbdManage.getPlanInfo(fileKey)
        //console.log("getDataBind", fileKey, JSON.stringify(control.fileInfo))
        control.widthNumber = control.fileInfo.WidthNumber
        control.heightNumber = control.fileInfo.HeightNumber
        //获取Page数据
        var netJsonary = fbdManage.getNetwork(fileKey)
        //console.log("netJsonary:", JSON.stringify(netJsonary))
        listModel.clear()

        for (var i = 0; i < netJsonary.length; i++) {
            //添加页面
            var netdata = netJsonary[i]
            listModel.append({
                                 "Type": netdata.Type,
                                 "Label": netdata.Label,
                                 "Number": netdata.Number,
                                 "Enable": netdata.Enable,
                                 "Height": netdata.Height
                             })
        }
    }

    //清空所有选中
    function clearSelect() {
        for (var n = 0; n < column.children.length; n++) {
            if (column.children[n] instanceof NEWFBDNetwork) {
                column.children[n].clearSelected()
            }
        }
    }

    //显示ST代码编辑器
    function showSTCodeDialog(data) {
        var c_cfcPropertyDialog = Qt.createComponent(
                    "qrc:/qml/NEWFBD/NEWFBDSTCodeDialog.qml")
        if (c_cfcPropertyDialog.status === Component.Ready) {
            control.showPopupDialog(c_cfcPropertyDialog)
            popupDialog.loadercenter.item.init(fileKey, data)
        }
    }

    //显示移动面板
    function showCopyMoveDialog() {
        var c_copyMoveDialog = Qt.createComponent(
                    "qrc:/qml/CFCEdit/CFCPageCopyAndMoveDialog.qml")
        if (c_copyMoveDialog.status === Component.Ready) {
            control.showPopupDialog(c_copyMoveDialog)
            popupDialog.loadercenter.item.init(fileKey)
        }
    }

    //显示文档信息面板
    function showFilePropertyDialog() {
        var c_cfcPropertyDialog = Qt.createComponent(
                    "qrc:/qml/NEWFBD/NEWFBDPropertiesDialog.qml")
        if (c_cfcPropertyDialog.status === Component.Ready) {
            control.showPopupDialog(c_cfcPropertyDialog)
            popupDialog.loadercenter.item.init(fileKey)
        }
    }

    //显示查询面板
    function showfindDialog() {
        var c_findDialog = Qt.createComponent(
                    "qrc:/qml/CFCEdit/CFCFindDialog.qml")
        if (c_findDialog.status === Component.Ready) {
            control.showPopupDialog(c_findDialog)
            popupDialog.loadercenter.item.init(fileKey)
        }
    }

    function showComponentPropertyDialog(data) {
        var c_componentPropertyDialog = Qt.createComponent(
                    "qrc:/qml/NEWFBD/NEWFBDComponentPropertiesDialog.qml")
        if (c_componentPropertyDialog.status === Component.Ready) {
            control.showPopupDialog(c_componentPropertyDialog)
            popupDialog.loadercenter.item.init(fileKey, data)
        }
    }

    //弹框显示
    function showPopupDialog(raiseItem) {
        popupDialog.raiseItem = raiseItem
        popupDialog.open()
    }

    //显示链接面板
    function showLinkToDialog(data) {
        var c_addVariableDialog = Qt.createComponent(
                    "qrc:/qml/NEWFBD/NEWFBDAddVariableDialog.qml")
        if (c_addVariableDialog.status === Component.Ready) {
            control.showPopupDialog(c_addVariableDialog)
            popupDialog.loadercenter.item.isProgram = (fileInfo.Type === "PROGRAM")
            popupDialog.loadercenter.item.fileKey = fileKey
            popupDialog.loadercenter.item.deviceName = deviceName
            popupDialog.loadercenter.item.paramData = data
            popupDialog.loadercenter.item.mainControl = control
        }
    }

    //显示添加变量面板
    function showAddVariableDialog(data) {
        var c_addVariableDialog = Qt.createComponent(
                    "qrc:/qml/NEWFBD/NEWFBDVariableInput.qml")
        if (c_addVariableDialog.status === Component.Ready) {
            control.showPopupDialog(c_addVariableDialog)
            popupDialog.loadercenter.item.fileKey = fileKey
            popupDialog.loadercenter.item.deviceName = deviceName
            popupDialog.loadercenter.item.paramData = data

            popupDialog.loadercenter.item.dataTypeList = VariableManage.getDataType(
                        model.deviceName).filter(function (item) {
                            return fileInfo.Type === "FUNCTION" ? (item.type === "BASE") : (item.type === "USER" || item.type === "BASE")
                        })

            popupDialog.loadercenter.item.owned = fileInfo.Name + "." + fileInfo.Code
            popupDialog.loadercenter.item.type = fileInfo.Type
            popupDialog.loadercenter.item.scopeList
                    = (fileInfo.Type === "PROGRAM" ? ["Local"] : (fileInfo.Type === "FUNCTION" ? ["Local", "Input", "Output"] : ["Local", "Input", "Output", "Static"]))
            popupDialog.loadercenter.item.getDataBind()
        }
    }

    //显示选择变量面板
    function showSelectVariableDialog(data) {
        //        var c_selectVariableDialog = Qt.createComponent(
        //                    "qrc:/qml/NEWFBD/NEWFBDSelectVariableDialog.qml")
        //        if (c_selectVariableDialog.status === Component.Ready) {
        //            control.showPopupDialog(c_selectVariableDialog)
        //            popupDialog.loadercenter.item.fileKey = fileKey
        //            popupDialog.loadercenter.item.deviceName = deviceName
        //            popupDialog.loadercenter.item.paramData = data
        //            popupDialog.loadercenter.item.fileName = fileInfo.Name + "." + fileInfo.Code
        //            popupDialog.loadercenter.item.fileType = fileInfo.Type

        //            popupDialog.loadercenter.item.getLocalList()
        //        }
        var c_selectVariableDialog = Qt.createComponent(
                    "qrc:/qml/variableshow/VariableSearchPanel.qml")
        if (c_selectVariableDialog.status === Component.Ready) {
            console.log("showSelectVariableDialog data", JSON.stringify(data))
            control.showPopupDialog(c_selectVariableDialog)
            popupDialog.loadercenter.item.deviceName = deviceName
            //popupDialog.loadercenter.item.fileName = fileInfo.Name + "." + fileInfo.Code
            popupDialog.loadercenter.item.checkAddress = true
            popupDialog.loadercenter.item.checkValue = true
            popupDialog.loadercenter.item.checkInOut = (data.LeftOrRight === 0 ? "I" : "O")
            //带入原有名称
            popupDialog.loadercenter.item.defaultName
                    = (data.AuxContent === "???" ? "" : data.AuxContent)
            //带入目标数据类型
            popupDialog.loadercenter.item.defaultDataType = data.DataType

            popupDialog.loadercenter.item.searchowned
                    = ["Global.POE", "IOM.POE", fileInfo.Name + "." + fileInfo.Code]
            popupDialog.loadercenter.item.searchType = ["Global", "IO", "M", fileInfo.Type]
            popupDialog.loadercenter.item.checkAddress = false
            if (fileInfo.Type === "PROGRAM") {
                popupDialog.loadercenter.item.checkAddress = true
                popupDialog.loadercenter.item.searchscope = ["Global", "IO", "Local"]
            } else if (fileInfo.Type === "FUNCTIONBLOCK") {
                popupDialog.loadercenter.item.searchscope
                        = ["Local", "Input", "Output", "InOut", "Static"]
            } else {
                //FUNCTION
                popupDialog.loadercenter.item.searchscope = ["Local", "Input", "Output", "InOut"]
            }
            popupDialog.loadercenter.item.selectVariableOk.connect(
                        function (vardata) {
                            console.log("selectVariableOk", control.fileKey,
                                        data.NetworkNumber, data.VarCompNumber,
                                        vardata.datatype, data.DataType)
                            //判断地址 变量 和 常量
                            if (vardata.scope === "Constant") {
                                if (fbdManage.modifyVariableComponent(
                                            control.fileKey,
                                            data.NetworkNumber,
                                            data.VarCompNumber, vardata.scope,
                                            vardata.name, vardata.owned)) {
                                    popupDialog.close()
                                }
                            } else if (vardata.scope === "Address") {
                                if (fbdManage.modifyVariableComponent(
                                            control.fileKey,
                                            data.NetworkNumber,
                                            data.VarCompNumber, vardata.scope,
                                            vardata.name, vardata.owned)) {
                                    popupDialog.close()
                                }
                            } else {
                                if (vardata.datatype === data.DataType
                                        || (data.DataType === "ARRAY"
                                            && vardata.arraylength > 1)) {
                                    console.log("selectVariableOk",
                                                JSON.stringify(vardata))
                                    if (fbdManage.modifyVariableComponent(
                                                control.fileKey,
                                                data.NetworkNumber,
                                                data.VarCompNumber,
                                                vardata.scope, vardata.name,
                                                vardata.owned)) {
                                        popupDialog.close()
                                    }
                                }
                            }
                        })
            popupDialog.loadercenter.item.clearVariable.connect(function () {
                console.log("clearVariable", control.fileKey,
                            data.NetworkNumber, data.VarCompNumber)
                if (fbdManage.clearVariableComponent(control.fileKey,
                                                     data.NetworkNumber,
                                                     data.VarCompNumber)) {
                    popupDialog.close()
                }
            })
            popupDialog.loadercenter.item.selectVariableCancel.connect(
                        function () {
                            console.log("popupDialog.close()")
                            popupDialog.close()
                        })

            popupDialog.loadercenter.item.getBindData()
        }
    }

    //显示接口面板
    function showFBInterfaceDialog() {
        var c_FBInterfaceDialog = Qt.createComponent(
                    "qrc:/qml/CFCEdit/CFCFBInterfaceDialog.qml")
        if (c_FBInterfaceDialog.status === Component.Ready) {
            control.showPopupDialog(c_FBInterfaceDialog)
            popupDialog.loadercenter.item.fileKey = fileKey
            popupDialog.loadercenter.item.deviceName = deviceName
        }
    }

    //建立链接
    function addConnection(seconddata) {
        if (!control.isLinking) {
            //第一次选中引脚
            control.firstConnectorData = seconddata
            control.isLinking = true
        } else {
            //console.log("link:", JSON.stringify(control.firstConnectorData),JSON.stringify(seconddata))
            //输出多个 输入一个 不能变量到变量 不能自己和自己联
            if (fbdManage.addConnection(control.fileKey,
                                        control.firstConnectorData,
                                        seconddata)) {
                console.log("链接成功")
                control.isLinking = false
                control.firstConnectorData = {}
            } else {
                console.log("链接失败")
            }
        }
    }

    //获取所有block元件名称列表
    function getAllBlockFunction() {
        blockfunctionList = []
        console.log(GlobalVariable.activedDevice)
        var data = serviceInterface.getAllFunctionAndBlock(
                    GlobalVariable.activedDevice)
        for (var i = 0; i < data["firmfunction"].count; i++) {
            //            var obj = {
            //                "name": data["firmfunction"][i].name,
            //                "value": data["firmfunction"][i].name
            //            }
            blockfunctionList.push(data["firmfunction"][i].name)
        }

        return blockfunctionList
    }

    //根据名称获取索引
    function getBlockIndexFromName(name) {
        for (var i = 0; i < blockfunctionList.length; i++) {
            if (blockfunctionList[i] === name) {
                return i
            }
        }
        return 0
    }
    //根据索引获取名称
    function getBlockNameFromIndex(index) {
        return blockfunctionList[index]
    }

    // 根据文本查询引脚变量所在的块
    function searchBlock(searchText) {
        setSearchComs(searchText)
        if (includeComs.length > 0) {
            if (currentSearchIndex > (includeComs.length - 1)) {
                currentSearchIndex = 0
            } else if (currentSearchIndex < 0) {
                currentSearchIndex = includeComs.length - 1
            }

            search_count.text = (currentSearchIndex + 1) + "/" + includeComs.length
            revealPosition(includeComs[currentSearchIndex].InstanceName)
        } else {
            clearSelect()
            search_count.text = "0/0"
        }
    }

    // 设置查询的组件
    function setSearchComs(searchText) {
        includeComs = []
        if (searchText.length <= 0) {
            clearSelect()
            search_count.text = "0/0"
            return
        }

        includeComs = findBlock(searchText)
    }


    /**
     * 查找块元件
     * searchText： 查找的文本
     */
    function findBlock(searchText) {
        // 所有的网络信息
        const nets = fbdManage.getNetwork(fileKey)
        // 所有块元件信息
        let components = []
        // 包含查询的文本块元件id
        let numbers = []
        var component
        let cIndex = 0
        let findComs = []

        // 遍历网络,获取每个网络下的所有块元件
        for (var nIndex = 0; nIndex < nets.length; nIndex++) {
            components.push(...fbdManage.getComponentConnector(
                                fileKey, nets[nIndex].Number))
        }

        for (; cIndex < components.length; cIndex++) {
            component = components[cIndex]

            if (component.Type.toLowerCase() === "variable") {
                if (component.AuxContent && component.AuxContent.toLowerCase(
                            ).includes(searchText)) {
                    // 记录引脚所在的块元件id
                    numbers.push(component.ParentNumber)
                }
            } else if (component.Name && component.Name.toLowerCase().includes(
                           searchText)) {
                numbers.push(component.Number)
            }
        }

        for (cIndex = 0; cIndex < components.length; cIndex++) {
            component = components[cIndex]

            if (numbers.includes(component.Number)) {
                findComs.push(component)
            }
        }

        return findComs
    }


    /**
     * 查找变量
     * searchText： 查找的文本
     */
    function findVariable(searchText) {
        const searchowned = ["Global.POE", "IOM.POE", fileInfo.Name + "." + fileInfo.Code]
        // let searchType = ["Global", "IO", "M", fileInfo.Type]
        let searchscope = []

        if (fileInfo.Type === "PROGRAM") {
            searchscope = ["Global", "IO", "Local"]
        } else if (fileInfo.Type === "FUNCTIONBLOCK") {
            searchscope = ["Local", "Input", "Output", "InOut", "Static"]
        } else {
            //FUNCTION
            searchscope = ["Local", "Input", "Output", "InOut"]
        }

        // 查找到的变量
        let findVars = []
        // 获取变量
        const variables = VariableManage.getVariableListWithScopeAndOwned(
                            deviceName, searchscope, searchowned)

        for (var vIndex = 0; vIndex < variables.length; vIndex++) {
            const variable = variables[vIndex]

            if (variable.name.toLowerCase() === searchText) {
                findVars.push(variable)
            }
        }

        return findVars
    }


    /**
     * 查找引脚变量
     * searchText： 查找的文本
     */
    function findPinVariable(searchText) {
        // 所有的网络信息
        const nets = fbdManage.getNetwork(fileKey)
        // 所有块元件信息
        let components = []
        let findComs = []

        // 遍历网络,获取每个网络下的所有块元件
        for (var nIndex = 0; nIndex < nets.length; nIndex++) {
            components.push(...fbdManage.getComponentConnector(
                                fileKey, nets[nIndex].Number))
        }

        for (var cIndex = 0; cIndex < components.length; cIndex++) {
            const component = components[cIndex]

            if (component.Type.toLowerCase() === "variable") {
                if (component.AuxContent && component.AuxContent.toLowerCase(
                            ).includes(searchText)) {
                    findComs.push(component)
                }
            }
        }

        return findComs
    }

    // 显示指定块名称所在的位置
    function revealPosition(blockName) {
        for (var n = 0; n < column.children.length; n++) {
            if (column.children[n] instanceof NEWFBDNetwork) {
                column.children[n].revealPosition(blockName)
            }
        }
    }

    // 设置画布位置
    function setCanvasPosition(xPos, yPos, width, height, offsetHeight) {
        const x = xPos * config.cellWidth
        const y = yPos * config.cellHeight
        width = width * config.cellWidth
        height = height * config.cellHeight

        root.x = -(x - width)
        root.y = -(y - btnRect.height - 10 - height + offsetHeight)
    }
}
