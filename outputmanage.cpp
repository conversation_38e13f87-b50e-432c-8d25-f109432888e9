#include <QJsonObject>
#include <QFile>
#include <QJsonDocument>
#include "outputmanage.h"

// 当接收页面请求时，一次发送多少条日志到页面
void OutputManage::setMsgNumber(int max)
{
    // qInfo() << __LINE__ << __func__ << "m_maxMsgNumber" << max;
    QMutexLocker locker(&m_mutex);
    m_maxMsgNumber = max;
}

// 导出上一次发送给页面的日志文件
void OutputManage::exportLogFile(const QString &filePath)
{
    // qInfo() << __LINE__ << __func__ << "logArray" << logArray.size();
    QMutexLocker locker(&m_mutex);
    // 生成的文件名为当前时间
    QString fileName = filePath + QDir::separator() + QDateTime::currentDateTime().toString("yyyy-MM-dd-hh-mm-ss") + ".log";
    fileName = QDir::toNativeSeparators(fileName);
    QFile file(fileName);
    qInfo() << "导出日志文件路径" << fileName;
    if (!file.open(QIODevice::WriteOnly))
    {
        qInfo() << "打开文件失败" << file.errorString();
        return;
    }
    // 从logArray中取出数据写入文件
    QJsonDocument doc;
    doc.setArray(logArray);
    file.write(doc.toJson());
    file.close();
    logArray = QJsonArray();
    // qInfo() << __LINE__ << __func__ << "logArray" << logArray.size();
}

// 清空
void OutputManage::clear()
{
    // qInfo() << __LINE__ << __func__ << "m_logQueue" << m_logQueue.size();
    // qInfo() << __LINE__ << __func__ << "logArray" << logArray.size();
    QMutexLocker locker(&m_mutex);
    // 清空队列缓存
    m_logQueue.clear();
    logArray = QJsonArray();
    // qInfo() << __LINE__ << __func__ << "m_logQueue" << logArray.size();
    // qInfo() << __LINE__ << __func__ << "logArray" << m_logQueue.size();
}

// 发送日志
QJsonArray OutputManage::sendLogToUI()
{
    // qInfo() << __LINE__ << __func__ << "m_logQueue" << m_logQueue.size();
    // qInfo() << __LINE__ << __func__ << "logArray" << logArray.size();
    QMutexLocker locker(&m_mutex);
    QJsonArray logArrayTemp = QJsonArray();
    while (!m_logQueue.isEmpty() && m_logQueue.size() < m_maxMsgNumber)
    {
        LogMessage logMsg = m_logQueue.dequeue();
        QJsonObject logObject;
        logObject["timestamp"] = logMsg.timestamp.toString();
        logObject["level"] = static_cast<int>(logMsg.level);
        logObject["message"] = logMsg.message;
        logObject["source"] = logMsg.source;
        // qDebug() << "sendLogToUI :" << logMsg.timestamp.toString() << static_cast<int>(logMsg.level) << logMsg.message << logMsg.source;

        logArray.append(logObject);
        logArrayTemp.append(logObject);
        // qDebug() << "sendLogToUI" << logArray;
    }
    // qInfo() << __LINE__ << __func__ << "m_logQueue" << m_logQueue.size();
    // qInfo() << __LINE__ << __func__ << "logArray" << logArray.size();

    return logArrayTemp;
}

// 接收日志
void OutputManage::receiveLog(const QString levelStr, QString message, QString source)
{
    // qInfo() << __LINE__ << __func__ << "m_logQueue" << m_logQueue.size();
    // qInfo() << __LINE__ << __func__ << "logArray" << logArray.size();

    // 将字符串转换为枚举类型
    LogLevel level = LogLevel::Info;
    if (levelStr == "Trace")
    {
        level = LogLevel::Trace;
    }
    else if (levelStr == "Debug")
    {
        level = LogLevel::Debug;
    }
    else if (levelStr == "Info")
    {
        level = LogLevel::Info;
    }
    else if (levelStr == "Warn")
    {
        level = LogLevel::Warn;
    }
    else if (levelStr == "Error")
    {
        level = LogLevel::Error;
    }
    else if (levelStr == "Critical")
    {
        level = LogLevel::Critical;
    }
    // 加锁
    QMutexLocker locker(&m_mutex);
    // 获取当前时间
    QDateTime currentDateTime = QDateTime::currentDateTime();
    // 构造日志消息
    LogMessage logMsg;
    logMsg.timestamp = currentDateTime;
    logMsg.level = level;
    logMsg.message = message;
    logMsg.source = source;
    // 将日志消息存入队列缓存
    m_logQueue.enqueue(logMsg);
    // qDebug() << "receiveLog :" << currentDateTime << static_cast<int>(level) << message << source;
    // qInfo() << __LINE__ << __func__ << "m_logQueue" << m_logQueue.size();
}
