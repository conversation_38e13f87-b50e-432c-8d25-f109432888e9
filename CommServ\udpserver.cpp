﻿/******************************************************************************
 * Copyright 2022-2023 Three Gorges Intelligent Industrial Control Technology Co., Ltd.
 * All right reserved. See COPYRIGHT for detailed Information.
 *
 * @file       udpserver.cpp
 * @brief      XXXX Function
 *
 * <AUTHOR>
 * @date       2023/09/20
 * @history
 *****************************************************************************/
#include "udpserver.h"
#include "protocol.h"
void UdpServer::start(){
    udpSocket = new QUdpSocket(this);
    udpSocket->bind(QHostAddress::Any, 1234);
    qDebug() << "start server";
}

void UdpServer::start(quint16 port){
    udpSocket = new QUdpSocket(this);
    udpSocket->bind(QHostAddress::Any, port);
    udpSocket->setParent(nullptr);
    qDebug() << "[INFO] START_SERVER" << port;
    Send = true;
    Recv = true;
    flag.storeRelaxed(true);
    //processPendingDatagrams();
}

void UdpServer::stop() {
    running = false;
    Send = false;
    Recv = false;
    flag.storeRelaxed(false);
    emit finished();
    //QThread::currentThread()->requestInterruption();
}

void UdpServer::sendMessage(const QByteArray& message,
                            const QHostAddress &address,
                            const quint16& port){
    QMutexLocker locker(&mutex);
    udpSocket->writeDatagram(message, address, port);
}

const quint16& UdpServer::getPort(){
    return myPort;
}

void UdpServer::setIndex(const int& idx){
    index = idx;
}

void UdpServer::SendMessage(){    
    QSharedPointer<MessageQueue> MsgQueue = Msgqueues.getSendQueue(index);
    if(MsgQueue.isNull())
        return;
    while(Send){
        if(!MsgQueue.data()->isEmpty()){
            Message safeMsg = MsgQueue.data()->dequeueMessage();
            //qDebug() << "[INFO] SendMessage In Udp";
            sendMessage(safeMsg.getMsg(), safeMsg.getIpAddress(), safeMsg.getPort());
        }
        QThread::msleep(10);
    }
}

void UdpServer::RecvMessage(){
    QByteArray datagram; QHostAddress senderIP; quint16 senderPort;
    QSharedPointer<MessageQueue> MsgQueue = Msgqueues.getRecvQueue(index);
    int dataSize;
    //qDebug() << "[INFO] MsgQueue In UdpServer" << MsgQueue.isNull() << index;
    if(MsgQueue.isNull())
        return;
    //qDebug() << "[INFO] MsgQueue In UdpServer not Null";
    while(flag.loadAcquire()){
        //QMutexLocker locker(&mutex);
        if (udpSocket->hasPendingDatagrams()) {
            datagram.clear(); senderIP.clear(); senderPort = 0;
            dataSize = udpSocket->pendingDatagramSize();
            datagram.resize(dataSize);
            udpSocket->readDatagram(datagram.data(), datagram.size(), &senderIP, &senderPort);
            if(dataSize > 0){
                MsgQueue.data()->enqueueMessage(Message(senderIP, senderPort, datagram));
                // if (index == 2){
                //     // qDebug() << "[INFO] recv data"  << datagram.toHex(' ')
                //     //         << " ServerId:" << index
                //     //         << senderIP.toString() << senderPort;
                // }
            }
        }
        QThread::msleep(10);
            // 处理接收到的数据
            // ...
    }
    qDebug() << "[INFO] RECV_QUIT";
}
